<?php

namespace App\Services\Sales\Strategies\KpiCalculation;

use App\Services\Sales\SalesIncentiveHolder;
use App\Services\Enums\KPITypes;

/**
 * Class ManagerialKpiStrategy
 *
 * Calculates KPI incentive values for managerial KPIs such as COACHING_RATIO, COVERED_COACHING, etc.
 * These KPIs often have specific incentive values defined for various achievement percentages.
 */
class ManagerialKpiStrategy implements KpiCalculationStrategy
{
    /**
     * Calculates the KPI value for managerial types.
     *
     * The calculation involves fetching a KPI incentive schema based on the role, KPI type,
     * and achievement percentage. The value from this schema is then returned.
     *
     * @param KPITypes $type The specific KPI type (e.g., KPITypes::COACHING_RATIO).
     * @param float $percent The achievement percentage for this KPI.
     * @param int|null $roleId The role ID of the user/entity, used to fetch role-specific incentives.
     * @param SalesIncentiveHolder $incentiveHolder Instance to access incentive schemas.
     * @return float The calculated KPI incentive value, or 0.0 if no specific incentive is found for the given parameters.
     */
    public function calculate(KPITypes $type, float $percent, ?int $roleId, SalesIncentiveHolder $incentiveHolder): float
    {
        // Logic for COACHING_RATIO, COVERED_COACHING, MANAGER_COVERAGE, M_K, VACANT_RATIO
        // from IncentiveCalculations::calculateKpiValue
        $kpi_incentive = $incentiveHolder->getKpiIncentiveAboveMR($roleId, $type);

        // Ensure $kpi_incentive is not null and return its value, otherwise default to 0.0.
        if ($kpi_incentive) {
            return $kpi_incentive->value ?? 0.0;
        }

        return 0.0;
    }
}
