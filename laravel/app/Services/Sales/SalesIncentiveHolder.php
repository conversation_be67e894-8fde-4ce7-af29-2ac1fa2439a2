<?php

namespace App\Services\Sales;

use App\Models\HigherOrderIncentiveSchema;
use App\Models\IncentiveMapping;
use App\Models\KpiIncentiveSchema;
use App\Models\PostVisitKpi;
use App\Services\Enums\KPITypes;
use App\Services\Reports\SalesIncentives\Traits\IncentiveCalculations;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
// use Illuminate\Support\Facades\Log; // Not directly used, consider removing if no debugging.

/**
 * Class SalesIncentiveHolder
 *
 * Holds and provides access to various incentive-related configurations and data.
 * This class is responsible for initializing and caching incentive schemas, KPI schemas,
 * higher-order incentives, and post-visit KPI data. It also manages the tracking of
 * manager-specific incentives through ManagerIncentiveTracker and utilizes the
 * IncentiveCalculations trait for common calculation logic.
 */
class SalesIncentiveHolder
{
    use IncentiveCalculations; // Provides common incentive calculation methods.

    private int $cacheTimeout;
    private string $fromYear;
    private array $months;

    /**
     * Collection of all IncentiveMapping records.
     * @var Collection<int, IncentiveMapping>
     */
    private readonly Collection $incentives;

    /**
     * Collection of all KpiIncentiveSchema records.
     * @var Collection<int, KpiIncentiveSchema>
     */
    private readonly Collection $kpiIncentives;

    /**
     * Collection of all HigherOrderIncentiveSchema records.
     * @var Collection<int, HigherOrderIncentiveSchema>
     */
    private readonly Collection $higherOrderIncentives;

    /**
     * Collection of aggregated PostVisitKpi records.
     * @var Collection<int, PostVisitKpi>
     */
    private readonly Collection $postKpi;

    private ManagerIncentiveTracker $managerIncentiveTracker;

    /**
     * Sets the cache timeout duration.
     *
     * @param int $cacheTimeout Duration in seconds.
     * @return self
     */
    public function setCacheTimeout(int $cacheTimeout): self
    {
        $this->cacheTimeout = $cacheTimeout;
        return $this;
    }

    /**
     * Sets the year from which incentive data should be considered.
     *
     * @param string $fromYear The year string (e.g., "2023").
     * @return self
     */
    public function setFromYear(string $fromYear): self
    {
        $this->fromYear = $fromYear;
        return $this;
    }

    /**
     * Sets the months for which incentive data should be filtered.
     *
     * @param array $months An array of month numbers (e.g., [1, 2, 3]).
     * @return self
     */
    public function setMonths(array $months): self
    {
        $this->months = $months;
        return $this;
    }

    /**
     * Initializes the SalesIncentiveHolder with necessary configurations and preloads data.
     * This method fetches and caches all required incentive schemas and KPI data.
     *
     * @param array $config Configuration array. Expected keys: 'cacheTimeout', 'fromYear', 'months'.
     * @return void
     */
    public function init(array $config): void
    {
        $this->cacheTimeout = $config['cacheTimeout'];
        $this->fromYear = $config['fromYear'];
        $this->months = $config['months']; // Assumes 'months' is part of the $config array passed in.

        // Preload all relevant data from cache or database.
        $this->postKpi = $this->getPostKpis();
        $this->incentives = $this->getIncentive();
        $this->kpiIncentives = $this->getKpiIncentiveSchema();
        $this->higherOrderIncentives = $this->getHigherOrderIncentives();

        $this->managerIncentiveTracker = new ManagerIncentiveTracker();
    }

    // Delegated methods for ManagerIncentiveTracker

    /**
     * Retrieves all tracked manager incentives.
     * Delegates to ManagerIncentiveTracker.
     *
     * @return array An array of all tracked manager incentives.
     */
    public function getTrackedManagerIncentives(): array
    {
        return $this->managerIncentiveTracker->getAll();
    }

    /**
     * Adds or updates a manager's incentive data.
     * Delegates to ManagerIncentiveTracker, determining the default ratio from higher-order incentives.
     *
     * @param int|null $managerId The ID of the manager.
     * @param int|null $roleId The role ID of the manager.
     * @param float $value The incentive value to add.
     * @return void
     */
    public function trackManagerIncentive(?int $managerId, ?int $roleId, float $value): void
    {
        $defaultRatio = $this->getFirstHighOrderIncentiveSchema($roleId)?->value ?? 0.0;
        $this->managerIncentiveTracker->add($managerId, $roleId, $value, $defaultRatio);
    }

    /**
     * Removes a manager's incentive data from tracking.
     * Delegates to ManagerIncentiveTracker.
     *
     * @param int $managerId The ID of the manager to remove.
     * @return void
     */
    public function removeTrackedManagerIncentive(int $managerId): void
    {
        $this->managerIncentiveTracker->remove($managerId);
    }

    /**
     * Decreases the size for a manager's incentive.
     * Delegates to ManagerIncentiveTracker.
     *
     * @param int $managerId The ID of the manager.
     * @return void
     */
    public function decreaseTrackedManagerIncentiveSize(int $managerId): void
    {
        $this->managerIncentiveTracker->decreaseSize($managerId);
    }
    // End of delegated methods

    /**
     * Gets the first incentive mapping for a given role ID.
     *
     * @param int|null $roleId The role ID.
     * @return IncentiveMapping|null The first matching incentive mapping, or null.
     */
    public function getFirstIncentive(?int $roleId): ?IncentiveMapping
    {
        return $this->incentives
            ->where('role_id', $roleId)
            ->first();
    }

    /**
     * Gets the first higher-order incentive schema for a given role ID.
     *
     * @param int|null $roleId The role ID.
     * @return HigherOrderIncentiveSchema|null The first matching higher-order schema, or null.
     */
    public function getFirstHighOrderIncentiveSchema(?int $roleId): ?HigherOrderIncentiveSchema
    {
        return $this->higherOrderIncentives->where('role_id', $roleId)->first();
    }

    /**
     * Gets the last incentive mapping for a given role ID.
     * (Assumes incentives are ordered appropriately, e.g., by achievement rule).
     *
     * @param int|null $roleId The role ID.
     * @return IncentiveMapping|null The last matching incentive mapping, or null.
     */
    public function getLastIncentive(?int $roleId): ?IncentiveMapping
    {
        return $this->incentives->where('role_id', $roleId)->last();
    }

    /**
     * Gets the incentive mapping for a specific achievement level and role ID.
     *
     * @param int|null $roleId The role ID.
     * @param mixed $achievement The achievement level to match.
     * @return IncentiveMapping|null The matching incentive mapping, or null.
     */
    public function getIncentiveOfAchievement(?int $roleId, $achievement): ?IncentiveMapping
    {
        return $this->incentives
            ->where('role_id', $roleId)
            ->where('achievement_rule', "=", $achievement)
            ->first();
    }

    /**
     * Gets the KPI incentive schema for a given role, KPI type, and achievement percentage.
     *
     * @param int|null $roleId The role ID.
     * @param KPITypes $type The KPI type.
     * @param float $percent The achievement percentage.
     * @return KpiIncentiveSchema|null The matching KPI incentive schema, or null.
     */
    public function getKpiIncentive(?int $roleId, KPITypes $type, float $percent): ?KpiIncentiveSchema
    {
        return $this->kpiIncentives
            ->where('role_id', $roleId)
            ->where('kpi_type', $type) // Assumes kpi_type in schema can be compared with KPITypes enum directly or its value
            ->where('from_percent', '<=', round($percent))
            ->where('to_percent', '>=', round($percent))
            ->first();
    }

    /**
     * Gets the KPI incentive schema that applies "above minimum requirement" for a role and KPI type.
     * This typically implies fetching the first (or only) rule defined for that KPI type and role.
     *
     * @param int|null $roleId The role ID.
     * @param KPITypes $type The KPI type.
     * @return KpiIncentiveSchema|null The matching KPI incentive schema, or null.
     */
    public function getKpiIncentiveAboveMR(?int $roleId, KPITypes $type): ?KpiIncentiveSchema
    {
        return $this->kpiIncentives
            ->where('role_id', $roleId)
            ->where('kpi_type', $type) // Assumes kpi_type in schema can be compared with KPITypes enum
            ->first();
    }

    /**
     * Gets the post-visit KPI data for a specific KPI type and user ID.
     *
     * @param KPITypes $type The KPI type.
     * @param int|null $userId The user ID.
     * @return PostVisitKpi|null The matching post-visit KPI data, or null.
     */
    public function getPostKpi(KpiTypes $type, ?int $userId): ?PostVisitKpi
    {
        return $this->postKpi->where('user_id', $userId)
            ->where('type', $type) // Assumes type in PostVisitKpi can be compared with KPITypes enum
            ->first();
    }

    /**
     * Retrieves and caches incentive mappings.
     * Uses tagged caching for easier invalidation.
     *
     * @return Collection<int, IncentiveMapping>
     */
    private function getIncentive(): Collection
    {
        return Cache::tags(['incentive-data'])->remember(
            'incentives:' . $this->fromYear, // Cache key includes year for specificity
            $this->cacheTimeout,
            fn() => IncentiveMapping::orderBy("achievement_rule")
                ->where('incentive_mappings.year', $this->fromYear)
                ->get()
        );
    }

    /**
     * Retrieves and caches KPI incentive schemas.
     * Uses tagged caching.
     *
     * @return Collection<int, KpiIncentiveSchema>
     */
    private function getKpiIncentiveSchema(): Collection
    {
        return Cache::tags(['kpi-data'])->remember(
            'kpi_incentives:' . $this->fromYear, // Cache key includes year
            $this->cacheTimeout,
            fn() => KpiIncentiveSchema::orderBy('from_percent')
                ->orderBy('to_percent')
                ->where('year', $this->fromYear)
                ->get()
        );
    }

    /**
     * Retrieves and caches higher-order incentive schemas.
     * Uses tagged caching.
     *
     * @return Collection<int, HigherOrderIncentiveSchema>
     */
    private function getHigherOrderIncentives(): Collection
    {
        return Cache::tags(['higher-order-incentive-data'])->remember(
            'higher_order_incentives:' . $this->fromYear, // Cache key includes year
            $this->cacheTimeout,
            fn() => HigherOrderIncentiveSchema::where('year', $this->fromYear)
                ->get()
        );
    }

    /**
     * Retrieves and caches aggregated post-visit KPI data.
     * Uses tagged caching.
     *
     * @return Collection<int, PostVisitKpi>
     */
    private function getPostKpis(): Collection
    {
        // Cache key incorporates year and specific months for granularity
        $cacheKey = 'post_kpis:' . $this->fromYear . '_months:' . implode('-', $this->months);
        return Cache::tags(['post-kpi-data'])->remember(
            $cacheKey,
            $this->cacheTimeout,
            fn() => PostVisitKpi::select('user_id', 'type', DB::raw('avg(percent) as percent'))
                ->whereYear('date', $this->fromYear)
                ->whereIn(DB::raw('MONTH(date)'), $this->months)
                ->groupBy('user_id', 'type')
                ->get()
        );
    }
}
