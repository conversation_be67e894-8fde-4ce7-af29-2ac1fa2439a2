<?php

namespace App\Imports;

use App\Account;
use App\AccountLines;
use App\AccountSocial;
use App\Doctor;
use App\DoctorSocial;
use App\Exceptions\CrmException;
use App\Helpers\CrmExcelDate;
use App\Helpers\ExcelImport;
use App\LineBricks;
use App\Models\NewAccountDoctor;
use App\ModelsImported;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\RequiredIf;

class AccountsImport extends ExcelImport
{

    /**
     * @return array
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function rules(array $row): array
    {
        $rules = array_merge(
            $this->doctorRules(),
            $this->doctorSocialRules(),
            $this->accountRules(),
            $this->accountTypeRules(),
            $this->accountSocialRules(),
            $this->accountLineRules($row),
            $this->accountDoctorsRules($row)
        );
        return $rules;
    }

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row): bool
    {
        try {
            $row['specialty_id'] = $row['specialty'];
            $modelImported = [];

            $doctor = Doctor::firstOrCreate([
                'ucode' => $row['ucode'],
            ], [
                'name' => $row['doctor_name'],
                'sub_speciality_id' => $row['sub_specialty'],
                'speciality_id' => $row['specialty_id'],
                'gender' => $row['gender'],
                'class_id' => $row['doctor_class_id'],
                'level_id' => $row['level'],
                'personality_type_id' => $row['personality_type'],
                'tel' => $row['doctor_tel'],
                'mobile' => $row['doctor_mobile'],
                'email' => $row['doctor_email'],
                'dob' => $row['date_of_birth'],
                'dom' => $row['date_of_marriage'],
                'active_date' => $row['doctor_active_date'],
                'inactive_date' => $row['doctor_inactive_date'],
                'file_id' => $this->file_id,
            ]);


            $modelImported[] = [
                'files_imported_id' => $this->file_id,
                'model_type' => ('App\Doctor'),
                'created_at' => now(),
                'updated_at' => now(),
            ];


            $row['type_id'] = $row['type'];

            $account = Account::firstOrCreate([
                'code' => $row['account_code'],
            ], [
                'type_id' => $row['type_id'],
                'sub_type_id' => $row['sub_type'],
                'classification_id' => $row['account_classification_id'],
                'name' => $row['account_name'],
                'address' => $row['address'],
                'tel' => $row['account_tel'],
                'mobile' => $row['account_mobile'],
                'email' => $row['account_email'],
                'website_link' => $row['website_link'],
                'notes' => $row['notes'],
                'active_date' => $row['account_active_date'],
                'inactive_date' => $row['account_inactive_date'],
                'file_id' => $this->file_id,
            ]);


            $modelImported[] = [
                'files_imported_id' => $this->file_id,
                'model_type' => ('App\Account'),
                'created_at' => now(),
                'updated_at' => now(),
            ];
            if ($row['line'] == null && $row['line_division'] == null) {
                $lineBricks = Cache::remember(
                    "line_bricks:" . $row['brick'],
                    now()->addHours(2),
                    fn() => LineBricks::where('brick_id', $row['brick'])->where('line_bricks.from_date', '<=', (string)Carbon::now())
                        ->where(fn($q) => $q->where('line_bricks.to_date', '=', null)->orWhere('line_bricks.to_date', '>=', (string)Carbon::now()))
                        ->get()
                );
                foreach ($lineBricks as $lineBrick) {
                    $this->addAccountLines($doctor, $account, $row, $lineBrick->line_id, $lineBrick->line_division_id);
                }
            } else {
                $this->addAccountLines($doctor, $account, $row);
            }

            $modelImported[] = [
                'files_imported_id' => $this->file_id,
                'model_type' => ('App\AccountLines'),
                'created_at' => now(),
                'updated_at' => now()
            ];

            $modelImported[] = [
                'files_imported_id' => $this->file_id,
                'model_type' => NewAccountDoctor::class,
                'created_at' => now(),
                'updated_at' => now()
            ];

            if ($row['account_social'] != null && $row['account_social'] != '') {
                AccountSocial::insert([
                    'account_id' => $account->id,
                    'social_id' => $row['account_social'],
                    'link' => $row['account_link'],
                    'file_id' => $this->file_id,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);


                $modelImported[] = [
                    'files_imported_id' => $this->file_id,
                    'model_type' => ('App\AccountSocial'),
                    'created_at' => now(),
                    'updated_at' => now()
                ];
            }


            if ($row['doctor_social'] != null && $row['doctor_social'] != '') {
                DoctorSocial::insert([
                    'doctor_id' => $doctor->id,
                    'social_id' => $row['doctor_social'],
                    'link' => $row['doctor_link'],
                    'file_id' => $this->file_id,
                    'created_at' => now(),
                    'updated_at' => now()
                ]);


                $modelImported[] = [
                    'files_imported_id' => $this->file_id,
                    'model_type' => ('App\DoctorSocial'),
                    'created_at' => now(),
                    'updated_at' => now()
                ];
            }
            Cache::remember(
                "account_lines_inserted_in_file:" . $this->file_id,
                now()->addHours(2),
                fn() => ModelsImported::insert($modelImported)
            );
        } catch (\Exception $e) {
            Log::error($e->getMessage());
            Log::error($e);
            return false;
        }

        return true;
    }

    private function addAccountLines(Doctor $doctor, Account $account, array $row, ?int $line_id = null, ?int $division_id = null)
    {
        $inserted = false;
        $lineId = $line_id ?? $row['line'];
        $divisionId = $division_id ?? $row['line_division'];

        $accountLines = AccountLines::where('account_id', $account->id)
            ->where('line_id', $lineId)
            ->where('line_division_id', $divisionId)
            ->where('brick_id', $row['brick'])
            ->firstOr(
                function () use ($account, $lineId, $divisionId, $row, $doctor, &$inserted) {
                    $data = AccountLines::create([
                        'account_id' => $account->id,
                        'line_id' => $lineId,
                        'line_division_id' => $divisionId,
                        'brick_id' => $row['brick'],
                        'class_id' => $row['class_id'],
                        'from_date' => $row['line_from_date'],
                        'to_date' => $row['line_to_date'],
                        'll' => $row['account_ll'],
                        'lg' => $row['account_lg'],
                        'file_id' => $this->file_id,
                    ]);
                    NewAccountDoctor::insert([
                        'account_id' => $account->id,
                        'doctor_id' => $doctor->id,
                        'line_id' => $lineId,
                        'account_lines_id' => $data->id,
                        'class_id' => $row['doctor_class_id'],
                        'from_date' => $row['doctor_from_date'],
                        'to_date' => $row['doctor_to_date'],
                        'file_id' => $this->file_id,
                    ]);
                    $inserted = true;
                    return $data;
                }
            );
        if (!$inserted) {
            NewAccountDoctor::firstOrCreate([
                'account_id' => $account->id,
                'doctor_id' => $doctor->id,
                'line_id' => $lineId,
                'account_lines_id' => $accountLines->id,
            ], [
                'class_id' => $row['doctor_class_id'],
                'from_date' => $row['doctor_from_date'],
                'to_date' => $row['doctor_to_date'],
                'file_id' => $this->file_id,
            ]);
        }
    }

    /**
     * rules by model
     */
    private function doctorRules()
    {
        return [
            'ucode' => ['required', 'string'],
            'doctor_name' => ['required', 'string'],
            'gender' => ['required', 'in:male,female'],
            'specialty' => ['required', 'numeric', 'exists_not_soft_deleted:specialities,id'],
            'class_id' => ['required', 'numeric', 'exists_not_soft_deleted:classes,id'],
            'sub_speciality' => ['nullable', 'numeric', 'exists_not_soft_deleted:specialities,id'],
            'level' => ['nullable', 'numeric', 'exists_not_soft_deleted:levels,id'],
            'personality_type' => ['nullable', 'numeric', 'exists_not_soft_deleted:personality_types,id'],
            'doctor_tel' => ['nullable', 'numeric'],
            'doctor_mobile' => ['nullable', 'numeric'],
            'doctor_email' => ['nullable', 'email'],
            'date_of_birth' => ['nullable', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
            'date_of_marriage' => ['nullable', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
            'doctor_active_date' => ['required', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
            'doctor_inactive_date' => ['nullable', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
        ];
    }

    private function doctorSocialRules()
    {
        return [
            'doctor_social' => ['nullable', 'numeric', 'exists_not_soft_deleted:socials,id'],
            'doctor_link' => ['nullable', 'url'],
        ];
    }

    private function accountTypeRules()
    {
        return [
            'type' => ['required', 'numeric', 'exists_not_soft_deleted:account_types,id'],
            'sub_type' => ['nullable', 'numeric', 'exists_not_soft_deleted:account_types,id'],
        ];
    }

    private function accountRules()
    {
        return [
            'account_code' => ['required', 'string'],
            'account_name' => ['required', 'string'],
            'address' => ['nullable', 'string'],
            'account_tel' => ['nullable', 'numeric'],
            'account_mobile' => ['nullable', 'string'],
            'account_email' => ['nullable', 'email'],
            'website_link' => ['nullable', 'url'],
            'notes' => ['nullable'],
            'account_active_date' => ['required', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
            'account_inactive_date' => ['nullable', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
        ];
    }

    private function accountSocialRules()
    {
        return [
            'account_social' => ['nullable', 'numeric', 'exists_not_soft_deleted:socials,id'],
            'account_link' => ['nullable', 'url'],
        ];
    }

    private function accountLineRules($row)
    {
        return [
            'line' => [Rule::requiredIf(fn() => $row['line'] != null)],
            'line_division' => [Rule::requiredIf(fn() => $row['line_division'] != null)],
            'brick' => [Rule::requiredIf(fn() => $row['line'] == null && $row['line_division'] == null), function ($attribute, $value, $fail) use ($row) {
                if ($row['line'] != null && $row['line_division'] != null && $row['brick'] != null) {
                    $exists = LineBricks::where('brick_id', $row['brick'])
                        ->where('line_id', $row['line'])
                        ->where('line_division_id', $row['line_division'])
                        ->where('line_bricks.from_date', '<=', (string)Carbon::now())
                        ->where(fn($q) => $q->where('line_bricks.to_date', '=', null)->orWhere('line_bricks.to_date', '>=', (string)Carbon::now()))
                        ->exists();
                    if (!$exists) $fail('This brick id ' . $value . ' not linked with division_id ' . $row['line_division']);
                }
            }],
            'class_id' => ['nullable', 'numeric', 'exists_not_soft_deleted:classes,id'],
            'line_from_date' => ['required', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
            'line_to_date' => ['nullable', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT, 'after:line_from_date'],
        ];
    }

    private function accountDoctorsRules($row)
    {
        return [
            'line' => [Rule::requiredIf(fn() => $row['line'] != null)],
            'doctor_from_date' => ['required', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT],
            'doctor_to_date' => ['nullable', 'date_format:' . CrmExcelDate::OFFICIAL_FORMAT, 'after:line_from_date']
        ];
    }
}
