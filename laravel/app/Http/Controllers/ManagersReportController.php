<?php

namespace App\Http\Controllers;

use App\ActualVisit;
use App\CallRate;
use App\DivisionType;
use App\Exceptions\CrmException;
use App\Line;
use App\LineDivision;
use App\LineDivisionType;
use App\Models\Coaching\CoachingHeader;
use App\Models\Kpi;
use App\Models\OffDay;
use App\OwActualVisit;
use App\PlanVisit;
use App\Position;
use App\PublicHoliday;
use App\Services\ActualService;
use App\Services\Enums\KPITypes;
use App\Services\PostVisitKpisService;
use App\Shift;
use App\User;
use App\Vacation;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ManagersReportController extends ApiController
{
    public function __construct(private readonly PostVisitKpisService $postVisitKpisService) {}
    public function lines()
    {
        /**@var User $user */
        $user = Auth::user();
        $lines = $user->userLines();
        $models = [
            [
                'name' => 'Division Type',
                'type' => DivisionType::class
            ],
            [
                'name' => 'Position',
                'type' => Position::class
            ],
        ];
        $shifts = Shift::select('id', 'name')->get();
        return $this->respond(compact('lines', 'models', 'shifts'));
    }
    public function positions(Request $request)
    {
        $lines = Line::when(!empty($request->lines), fn($q) => $q->whereIntegerInRaw("lines.id", $request->lines))->get()->pluck('id');
        if ($request->type == DivisionType::class) {
            $lineDivisionTypes = LineDivisionType::whereIn('line_id', $lines)
                ->where('from_date', '<=', now())
                ->where(fn($q) => $q->where('to_date', null)->orWhere('to_date', '>=', now()))
                ->with('divisiontype')->whereHas("divisiontype", function ($q) {
                    $q->where("last_level", "<>", 1);
                })->get()->pluck('divisiontype')->map(function ($divisionType) {
                    return [
                        'id' => $divisionType->id,
                        'name' => $divisionType->name,
                        'type' => DivisionType::class,
                    ];
                });
            return $this->respond($lineDivisionTypes->unique('id')->values());
        }
        if ($request->type == Position::class) {
            $linePositions = Position::get()->map(function ($position) {
                return [
                    'id' => $position->id,
                    'name' => $position->name,
                    'type' => Position::class,
                ];
            });
            return $this->respond($linePositions->unique('id')->values());
        }
    }

    public function filter(Request $request)
    {
        /**@var User */
        $authUser = Auth::user();
        $visit = $request->visitFilter;
        $allTypes = DivisionType::select('id', 'name', 'single_target_days', 'double_target_days', 'target_days', 'last_level')->get();
        $allPositions = Position::select('id', 'name', 'single_target_days', 'double_target_days', 'target_days')->get();
        $division_type = $allTypes->where('last_level', '=', 1)->value('id');
        $from = Carbon::parse($visit['fromDate'])->startOfDay();
        $to = Carbon::parse($visit['toDate'])->endOfDay();
        $month = [Carbon::parse($from)->format('m'), Carbon::parse($to)->format('m')];
        $year = Carbon::parse($from)->format('Y');
        $lines = Line::when(!empty($visit['line']), fn($q) => $q->whereIntegerInRaw("lines.id", $visit['line']))->get();
        $shifts = Shift::when(!empty($visit['shifts']), fn($q) => $q->whereIntegerInRaw("shifts.id", $visit['shifts']))->get();
        $shiftIds = $shifts->pluck('id')->toArray();
        $fields = collect([
            'id',
            'line',
            'division',
            'employee',
            'emp_code',
            'position',
            'wd',
            'vac',
            'activities',
            'net_days',
            'target_days',
            'actual_days',
            'am_target',
            'am_achieve',
            'pm_target',
            'pm_achieve',
            'coaching_target',
            'coaching',
            'covered_coaching',
            'coaching_days',
            'coaching_ratio',
            'vacant_count',
            'vacant_covered',
            'vacant_ratio',
            'head_ratio',
            's_v',
            's_day_target',
            's_day',
            's_achieve',
            'd_plan',
            'd_v_mention',
            'd_v_mention_ratio',
            'd_v',
            'd_day_target',
            'd_day',
            'd_achieve',
        ]);
        $clickable_fields = collect(['coaching', 's_v', 'd_v', 'd_v_mention']);

        foreach ($shifts as $shift) {
            $fields = $fields->merge($shift->name . '_visits');
            $clickable_fields = $clickable_fields->merge($shift->name . '_visits');
        }
        $fields = $fields->merge('direct_emp');
        $users = collect([]);
        if ($visit['type'] == DivisionType::class) {
            $divisions = collect([]);
            foreach ($lines as $line) {
                $divisions = $divisions->merge($authUser->userDivisions($line, $from, $to))
                    ->prepend($authUser->divisions($from, $to)->where('line_divisions.line_id', $line->id)->where('is_kol', 0)->first());
            }
            if (!empty($visit['positions'])) {
                $divisions = $divisions->whereIn('division_type_id', $visit['positions']);
            } else {
                $divisions = $divisions->where('division_type_id', '<>', $division_type);
            }
            // throw new CrmException($divisions);
            $divisions->values()->each(function ($division) use ($users, $from, $to) {
                $divisionUser = $division?->users($from, $to)?->first(['users.id', 'users.fullname', 'users.emp_code', 'users.menuroles']);
                $users = $users->push([
                    'id' => $divisionUser?->id,
                    'emp_code' => $divisionUser?->emp_code,
                    'fullname' => $divisionUser?->fullname,
                    'menuroles' => $divisionUser?->menuroles,
                    'type' => DivisionType::class,
                    'type_id' => $division?->division_type_id
                ]);
            });
            $users = $users->filter(fn($user) => $user['id'] != null)->unique('id')->values();
            // throw new CrmException($users);
        }
        if ($visit['type'] == Position::class) {
            $users = Position::with(['users' => function ($q) use ($visit) {
                if (!empty($visit['positions'])) {
                    $q->whereIn('position_id', $visit['positions']);
                }
            }])->get()->pluck('users')->collapse()->map(function ($user) {
                return [
                    'id' => $user?->id,
                    'emp_code' => $user?->emp_code,
                    'fullname' => $user?->fullname,
                    'menuroles' => $user?->menuroles,
                    'type' => Position::class,
                    'type_id' => $user?->position()?->id
                ];
            });
        }
        $months = [];
        $period = CarbonPeriod::create($from, '1 month', $to);
        foreach ($period as $date) {
            $months[] = $date->format('F'); // 'F' gives full month name, use 'm' for month number
        }
        $dates[] = [
            'Month' => implode(', ', $months),
            'Year' => $year,
        ];
        $data = collect([]);
        $users->each(function ($user) use (&$data, $from, $to, $visit, $shifts, $shiftIds, $month, $year, $allTypes, $allPositions, $division_type) {
            $data = $data->push($this->statistics($user, $visit, $from, $to, $shifts, $shiftIds, $month, $year, $allTypes, $allPositions, $division_type));
        });
        return $this->respond(['data' => $data, 'fields' => $fields, 'clickable_fields' => $clickable_fields, 'dates' => $dates]);
    }

    private function statistics($user, $visit, $from, $to, $shifts, $shiftIds, $month, $year, $allTypes, $allPositions, $division_type)
    {
        $object = User::find($user['id']);

        $lines = $object->lines($from, $to)->get();
        $line_ids = $lines->pluck('id')->toArray();
        $lineFirstId = !empty($line_ids) ? $line_ids[0] : null;

        $belowUsers = $object->allBelowUsers(from: $from, to: $to);

        $belowusersIds = $belowUsers->pluck('id');
        $coachingTarget = $belowusersIds->count();


        // $validBelowUsersCount = $this->getHeadCounts($belowUsers, $from);

        $division = $object->divisions($from, $to)->whereIntegerInRaw('line_divisions.line_id', $line_ids)->where('is_kol', 0)->where('division_type_id', '<>', 4)->first();
        $data = collect([]);
        $callrates = CallRate::select('call_rate', 'shift_id')->where('division_id', $division?->id)->whereBetween('date', [$from, $to])->get();
        $callRatesAMVisits = $callrates->where('shift_id', 1)->sum('call_rate');
        $callRatesPMVisits = $callrates->where('shift_id', 2)->sum('call_rate');

        $actuals = (new ActualService)->getActuals($object, 'users.id', $from, $to, $shiftIds, $line_ids);
        $coachings = CoachingHeader::where('evaluator_id', $object->id)->whereBetween('date', [$from, $to])
            ->whereIn('employee_id', $belowusersIds)
            ->get();
        $coachingNumber = $coachings->unique('employee_id')->values()->count();
        $coachingDays = $coachings->unique('date')->values()->count();
        $coveredCoaching = $coachingTarget > 0 ? round($coachingNumber / $coachingTarget * 100, 2) . '%' : 0 . '%';

        $vacations = $this->vacations($object, $visit, $from, $to, $month, $year);
        $wd = CallRate::workingDays($from, $to, $visit['shifts'], $lineFirstId);

        $ow = $this->ow($object, $from, $to, $visit);
        $net_days = $wd - $ow - $vacations;
        $coachingRatio = $net_days > 0 ? round($coachingDays / $net_days * 100, 2) : 0;

        // vacant users
        $belowDivisions = $object->allBelowDivisions(from: $from, to: $to)->whereIn('line_id', $line_ids)->where('is_kol', 0)
            ->where('division_type_id', $division_type)->unique('id')->pluck('id')->toArray();

        $vacantDivisions = LineDivision::whereDoesntHave('allUsers', function ($query) use ($from, $to) {
            $query->where(function ($query) use ($from, $to) {
                $query->where(function ($subQuery) use ($from, $to) {
                    $subQuery->whereNull('line_users_divisions.to_date') // Active records
                        ->orWhereBetween('line_users_divisions.to_date', [$from->toDateString(), $to->toDateString()]) // Ends within range
                        ->orWhere('line_users_divisions.to_date', '>=', $to->toDateString()); // Ends within range
                })
                    ->where(function ($subQuery) use ($from, $to) {
                        $subQuery->where('line_users_divisions.from_date', '<=', $from->toDateString()) // Starts before range
                            ->orWhereBetween('line_users_divisions.from_date', [$from->toDateString(), $to->toDateString()]); // Starts within range
                    });
            });
        })->whereIn('id', $belowDivisions)->get();
        $vacantCovered = 0;
        foreach ($vacantDivisions as $vacantDivision) {
            if (
                ActualVisit::where('user_id', $object->id)->whereBetween('visit_date', [$from, $to])
                ->where('div_id', $vacantDivision->id)->count() > 0
            ) {
                $vacantCovered += 1;
            }
        }
        $vacantCount = $vacantDivisions->count();
        $vacantRatio = $vacantCount > 0 ? round($vacantCovered / $vacantCount * 100) . "%" : 100 . '%';

        // Head Count
        $totalHead = $coachingTarget + $vacantCount;
        $headRatio = $totalHead > 0 ? round($coachingTarget / $totalHead * 100) . "%" : 100 . '%';

        $targetUnit = 0.0;
        if ($user['type'] == DivisionType::class) {
            $typesTargets = $allTypes->where('id', $user['type_id'])->first();
            $targetUnit = $typesTargets?->target_days;
            $singleTargetUnit = $typesTargets?->single_target_days;
            $doubleTargetUnit = $typesTargets?->double_target_days;
        } else {
            $positionsTargets = $allPositions->where('id', $user['type_id'])->first();
            $targetUnit = $positionsTargets?->target_days;
            $singleTargetUnit = $positionsTargets?->single_target_days;
            $doubleTargetUnit = $positionsTargets?->double_target_days;
        }
        $target_days = round($net_days * $targetUnit / 100, 0);
        $single_target_days = round($net_days * $singleTargetUnit / 100, 0);
        $double_target_days = round($net_days * $doubleTargetUnit / 100, 0);
        $actual_days = $actuals->pluck('date')->unique()->count();
        $targetAM = $callRatesAMVisits * $target_days;
        $totalAMVisits = $actuals->where('acc_shift_id', 1)->count();
        $totalPMVisits = $actuals->where('acc_shift_id', 2)->count();
        $targetPM = $callRatesPMVisits * $target_days;

        $singleVisits = $this->singleAndDoubleDays($actuals, 1);
        $doubleVisits = $this->singleAndDoubleDays($actuals, 2);


        $mrs = $object->belowUsersOfAllLinesWithPositions($lines, 'Active', $from, $to)->unique('id')->pluck('id');
        $doubleMentioned = $this->doubleVisitsManagers($mrs, $lines, $shiftIds, $object, $from, $to, 2);
        $doubleMentionedDaysCount = $this->countDoubleVisitManagersOverLimit($mrs, $lines, $shiftIds, $object, $from, $to, 2);
        // $doubleMentionedDaysCount = $doubleMentioned->pluck('visit_date')
        //     ->map(fn($date) => Carbon::parse($date)->toDateString()) // Converts to 'Y-m-d'
        //     ->unique()
        //     ->count();
        $doubleMentionedDaysRatio =  $net_days > 0 ? round($doubleMentionedDaysCount / $net_days * 100, 2) . "%" : 0 . '%';
        $data = collect([
            'id' => $user['id'],
            'line' => $lines->pluck('name')->implode(' , '),
            'division' => $division?->name ?? '',
            'employee' => $user['fullname'],
            'emp_code' => $user['emp_code'] ?? '',
            'position' => $user['menuroles'],
            'color' => $division?->DivisionType->color,
            'wd' => $wd,
            'vac' => $vacations,
            'activities' => $ow,
            'net_days' => $net_days,
            'target_days' => $target_days,
            'actual_days' => $actual_days,
            'am_target' => $targetAM,
            'am_achieve' => $targetAM > 0 ? round($totalAMVisits / $targetAM * 100, 2) . ' %' : 0 . ' %',
            'pm_target' => $targetPM,
            'pm_achieve' => $targetPM > 0 ? round($totalPMVisits / $targetPM * 100, 2) . ' %' : 0 . ' %',
            'coaching_target' => $coachingTarget,
            'coaching' => $coachingNumber,
            'covered_coaching' => $coveredCoaching,
            'coaching_days' => $coachingDays,
            'coaching_ratio' => $coachingRatio,
            'vacant_count' => $vacantCount,
            'vacant_covered' => $vacantCovered,
            'vacant_ratio' => $vacantRatio,
            'head_ratio' => $headRatio,
            's_v' => $actuals->where('visit_type_id', 1)->count(),
            's_day_target' => $single_target_days,
            's_day' => $singleVisits,
            's_achieve' => $singleVisits > 0 ?  round($single_target_days / $singleVisits * 100) . '%' : 0 . '%',
            'd_v' => $actuals->where('visit_type_id', 2)->count(),
            'd_day_target' => $double_target_days,
            'd_day' => $doubleVisits,
            'd_achieve' => $doubleVisits > 0 ?  round($double_target_days / $doubleVisits * 100) . '%' : 0 . '%',
            'd_v_mention' => $doubleMentioned->count(),
            'd_v_mention_ratio' => $doubleMentionedDaysRatio,
            'd_plan' => PlanVisit::where('user_id', $object->id)->whereBetween('visit_date', [$from, $to])
                ->where('visit_type', 2)->count(),

        ]);
        $shifts->each(function ($shift) use ($data, $actuals) {
            $data = $data->put($shift->name . '_visits', $actuals->where('acc_shift_id', $shift->id)->count());
        });
        return $data;
    }



    private function getHeadCounts($belowUsers, $from)
    {
        $validBelowUsersCount = 0;
        $fromMinus3 = $from->copy()->subMonths(3)->startOfDay()->toDateString();

        foreach ($belowUsers as $belowUser) {
            if ($belowUser->hiring_date) {
                $hiringDate = Carbon::parse($belowUser->hiring_date)->toDateString();
                if ($hiringDate <= $fromMinus3) {
                    $validBelowUsersCount++;
                }
            } else {
                $validBelowUsersCount++;
            }
        }
        return $validBelowUsersCount;
    }

    public function vacations($user, $visit, $from, $to, $month, $year)
    {
        return Vacation::getVacationsDatePerPeriod($user, $from, $to, $month, $year, $visit['shifts']);
    }
    public function ow($user, $from, $to, $visit)
    {
        $owVisits = OwActualVisit::select('ow_actual_visits.date as date', 'user_id', 'shift_id')
            ->leftJoin('office_work_types', 'ow_actual_visits.ow_type_id', 'office_work_types.id')
            ->where('office_work_types.with_deduct', '1')
            ->where('user_id', $user->id)->whereBetween('date', [$from, $to])
            ->where(fn($q) => $q->whereIn('shift_id', $visit['shifts'])->orWhere('shift_id', null))
            ->get();
        return $this->calculateOvertimeCount($owVisits, $visit);
    }
    private function calculateOvertimeCount($owVisits, $visit)
    {
        $count = 0.0;
        $shifts = count($visit['shifts']) > 1 ? [1, 2] : $visit['shifts'];
        foreach ($owVisits as $owVisit) {
            if ($owVisit->shift_id != null) {
                $count += $this->checkOffDays($owVisit, $shifts, $owVisit->shift_id);
            } else {
                foreach ($shifts as $shift) {
                    $count += $this->checkOffDays($owVisit, $shifts, $shift);
                }
            }
        }
        return $count;
    }
    public function checkOffDays($owVisit, $shifts, $shift)
    {
        $count = 0;
        if (
            !OffDay::isOffDay($owVisit->date, $owVisit->date, $owVisit->date, $shift)
            && !PublicHoliday::isPublicHoliday($owVisit->date, $owVisit->date, $owVisit->date)
        ) {
            count($shifts) > 1 ? $count = 0.5 : $count = 1.0;
        }
        return $count;
    }
    public function belowEmployeesData(Request $request)
    {
        $data = $request->data;
        $shifts = $request->shifts;
        $user = User::find($data['id']);
        $lines = $user->lines;
        $line_ids = $lines->pluck('id');
        $from = Carbon::parse($request->from)->startOfDay();
        $to = Carbon::parse($request->to)->endOfDay();
        $shifts = Shift::when(!empty($shifts), fn($q) => $q->whereIntegerInRaw("shifts.id", $shifts))->get()->pluck('id')->toArray();
        $below = $user->belowUsersOfAllLinesWithPositions($lines, 'Active', $from, $to)->map(function ($mr) use ($user, $lines, $line_ids, $from, $to, $shifts) {
            $mrDivisions = $mr->divisions($from, $to)->whereIntegerInRaw('line_divisions.line_id', $line_ids)->get();
            $visits = $this->getvisits($user, $mr, $from, $to, $shifts);
            return [
                'id' => $mr->id,
                'code' => $mr->code ?? '',
                'name' => $mr->fullname,
                'division' => $mrDivisions->pluck('name')->implode(' , '),
                'color' => $mrDivisions->first()?->DivisionType?->color,
                'number_of_visits' => $visits,
                'number_of_days' => $this->getvisitDays($user, $mr, $from, $to, $shifts),
                'ratio' => $visits != 0 ?
                    round(($visits / $this->doubleVisitsManagers([$mr], $lines, $shifts, $user, $from, $to, 2)->count()), 2) * 100 . '%'
                    : 0 . '%'
            ];
        });
        return $this->respond($below);
    }
    public function doubleVisitsManagers($mrs, $lines, $shifts, $user, $from, $to, $visit_type)
    {
        $visits = ActualVisit::whereIn('user_id', $mrs)
            ->where('visit_type_id', 2)
            ->whereBetween('visit_date', [$from, $to])
            ->whereHas('actualVisitManagers', fn($q) => $q->where('user_id', $user->id))
            ->whereHas('accountType', fn($q) => $q->whereIn('shift_id', $shifts))
            ->get();
        return $visits;
    }
    public function countDoubleVisitManagersOverLimit($mrs, $lines, $shifts, $user, $from, $to, $visit_type, $limit = 5)
    {
        // Reuse the existing function
        $visits = $this->doubleVisitsManagers($mrs, $lines, $shifts, $user, $from, $to, $visit_type);
        // Group visits by user_id and visit_date
        $grouped = $visits->groupBy(function ($visit) {
            return $visit->user_id . '_' . Carbon::parse($visit->visit_date)->toDateString();
        });
        // Count how many MRs had >= $limit visits in one day
        $total = 0;
        foreach ($grouped as $group) {
            if ($group->count() >= $limit) {
                $total += 1;
            }
        }

        return $total;
    }
    public function singleAndDoubleDays($actuals, $visit_type)
    {
        return $actuals->where('visit_type_id', $visit_type)->pluck('date')->unique()->count();
    }

    public function visitShift($actuals, $shift_id)
    {
        return $actuals->where('acc_shift_id', $shift_id)->count();
    }

    public function getVisits($user, $mr, $from, $to, $shifts)
    {
        $count = ActualVisit::where('user_id', $mr->id)
            ->where('visit_type_id', 2)->whereBetween('visit_date', [$from, $to])
            ->whereHas('actualVisitManagers', fn($q) => $q->where('user_id', $user->id))
            ->whereHas('accountType', fn($q) => $q->whereIn('shift_id', $shifts))
            ->count();
        return $count;
    }

    public function getVisitDays($user, $mr, $from, $to, $shifts)
    {
        return ActualVisit::select(DB::raw("(DATE_FORMAT(visit_date,'%Y-%m-%d')) as visit_date"))
            ->leftJoin('accounts', 'actual_visits.account_id', 'accounts.id')
            ->leftJoin('account_types', 'actual_visits.acc_type_id', 'account_types.id')
            ->where('user_id', $mr->id)
            ->whereHas('actualVisitManagers', fn($q) => $q->where('user_id', $user->id))
            ->whereIntegerInRaw('account_types.shift_id', $shifts)
            ->where('visit_type_id', 2)->whereBetween('actual_visits.visit_date', [$from, $to])
            ->get()->unique('visit_date')->count();
    }

    public function showData(Request $request)
    {
        $column = $request->column;
        $user = User::find($request->item['id']);
        $lines = $user->lines;
        $visitFilter = $request->visitFilter;
        $from = Carbon::parse($visitFilter['fromDate'])->startOfDay();
        $to = Carbon::parse($visitFilter['toDate'])->endOfDay();
        $mrs = $user->allBelowUsers(from: $from, to: $to)->unique('id')->pluck('id');
        $shifts = Shift::when(!empty($visitFilter['shifts']), fn($q) => $q->whereIntegerInRaw("shifts.id", $visitFilter['shifts']))->get();
        $shift_ids = $shifts->pluck('id')->toArray();
        $actuals = collect([]);
        foreach ($shifts as $shift) {
            if ($shift->name . '_visits' == $column) {
                $actuals = (new ActualService)->getActuals($user, 'users.id', $from, $to, [$shift->id]);
            }
        }

        if ($column == 's_v') {
            $actuals = (new ActualService)->getActuals($user, 'users.id', $from, $to)->where('visit_type_id', 1)->values();
        }
        if ($column == 'coaching') {
            $belowUsers = $user->allBelowUsers(from: $from, to: $to)->pluck('id');
            $coachings = CoachingHeader::where('evaluator_id', $user->id)->whereIn('employee_id', $belowUsers)->whereBetween('date', [$from, $to])->get()->unique('employee_id')->values()->map(function ($coaching) {
                return [
                    'id' => $coaching->id,
                    'date' => Carbon::parse($coaching->date)->toDateString(),
                    'coachee' => $coaching->evaluator->fullname,
                    'member' => $coaching->employee?->fullname,
                    'strenghts' => $coaching->strenghts,
                    'points_of_improvement' => $coaching->points_of_improvement,
                    'notes' => $coaching->notes,
                ];
            });
            return $this->respond($coachings);
        }
        if ($column == 'd_v') {
            $actuals = (new ActualService)->getActuals($user, 'users.id', $from, $to)->where('visit_type_id', 2)->values();
        }
        if ($column == 'd_v_mention') {
            $mrs = $user->belowUsersOfAllLinesWithPositions($lines, 'Active', $from, $to)->unique('id')->pluck('id');
            $actuals = ActualVisit::whereIn('user_id', $mrs)
                ->where('visit_type_id', 2)
                ->whereBetween('visit_date', [$from, $to])
                ->whereHas('actualVisitManagers', fn($q) => $q->where('user_id', $user->id))
                ->whereHas('accountType', fn($q) => $q->whereIn('shift_id', $shift_ids))
                ->with(['actualVisitManagers.user', 'speciality'])
                ->get()
                ->map(function (ActualVisit $visit) use ($user) {
                    return [
                        'id' => $visit->id,
                        'date' => $visit->visit_date,
                        'emp' => $visit->user->fullname,
                        'account' => $visit->account?->name,
                        'account_id' => $visit->account?->id,
                        'doctor' => $visit->doctor?->name,
                        'doctor_id' => $visit->doctor?->id,
                        'speciality' => $visit->speciality?->name,
                        'acc_type' => $visit->account?->type?->name,
                        'manager' => $visit->actualVisitManagers->where("user_id", $user->id)
                            ->first()->user?->fullname ?? '',
                    ];
                });
            return $this->respond($actuals);
        }
        return $this->respond($actuals->map(function ($visit) use ($user, $mrs) {
            return [
                'id' => $visit->id,
                'line' => $visit->line,
                'line_id' => $visit->line_id,
                'division' => $visit->division,
                'date' => $visit->visit_date,
                'emp' => $visit->employee,
                'type' => $visit->type,
                'account' => $visit->account,
                'account_id' => $visit->account_id,
                'doctor' => $visit->doctor,
                'doctor_id' => $visit->doctor_id,
                'speciality' => $visit->speciality,
                'acc_type' => $visit->acc_type,
                'member' =>  $visit->visit_type_id == 2 && !User::find($user->id)
                    ->divisionType(Line::find($visit->line_id))?->isLastLevel() ? ActualVisit::whereDate('visit_date', $visit->date)
                    ->where('account_id', $visit->account_id)->where('line_id', $visit->line_id)
                    ->where('account_dr_id', $visit->doctor_id)
                    ->whereIn('user_id', $mrs)->first()?->user?->fullname : $user->fullname,
            ];
        }));
    }

    public function post(Request $request)
    {
        $sopData = $request->data;
        $filters = $request->filters;
        // $date = Carbon::parse($filters['fromDate'])->startOfMonth()->toDateString();
        $from = Carbon::parse($filters['fromDate'])->startOfMonth()->toDateString();
        $to = Carbon::parse($filters['toDate'])->endOfMonth()->toDateString();
        $period = CarbonPeriod::create($from, '1 month', $to);
        if ($filters['view'] == 'SOP') {
            $postedData = [];
            $kpiVacantRatio = Kpi::where('name', 'Vacant Ratio')->first();
            $kpiCoachingRatio = Kpi::where('name', 'Coaching Ratio')->first();
            $kpiCoveredCoaching = Kpi::where('name', 'Covered Coaching')->first();
            $kpiHeadRatio = Kpi::where('name', 'Head Ratio')->first();
            $kpiDvMentionRatio = Kpi::where('name', 'D V Mention Ratio')->first();
            foreach ($sopData as $item) {
                // Calculate average
                $coaching_ratio = ($item['net_days']  == 0) ? 0 : ($item['coaching_days'] / ($item['net_days'] * 0.65)) * 100;

                // Add to new array
                $postedData[] = [
                    'id' => $item['id'],
                    'coaching_ratio' => round($coaching_ratio, 2),
                    'vacant_ratio' => $item['vacant_ratio'],
                    'covered_coaching' => $item['covered_coaching'],
                    'head_ratio' => $item['head_ratio'],
                    'd_v_mention_ratio' => $item['d_v_mention_ratio'],
                ];
            }
            Log::info($postedData);
            $this->postVisitKpisService->post(KPITypes::VACANT_RATIO, $postedData, $period, $kpiVacantRatio);
            $this->postVisitKpisService->post(KPITypes::COACHING_RATIO, $postedData, $period, $kpiCoachingRatio);
            $this->postVisitKpisService->post(KPITypes::COVERED_COACHING, $postedData, $period, $kpiCoveredCoaching);
            $this->postVisitKpisService->post(KPITypes::HEAD_RATIO, $postedData, $period, $kpiHeadRatio);
            $this->postVisitKpisService->post(KPITypes::D_V_MENTION_RATIO, $postedData, $period, $kpiDvMentionRatio);
        }
        if ($filters['view'] == 'Coverage') {
            $this->postVisitKpisService->post(KPITypes::MANAGER_COVERAGE, $sopData, $period);
        }
        return $this->respondSuccess();
    }
}
