<?php

namespace App\Http\Controllers;

use App\AccountType;
use App\ActualVisit;
use App\CallRate;
use App\DivisionType;
use App\Exceptions\CrmException;
use App\Http\Controllers\Controller;
use App\Line;
use App\Models\CommercialRequest\CommercialRequest;
use App\Models\Kpi;
use App\Models\OffDay;
use App\OwActualVisit;
use App\OwPlanVisit;
use App\Services\ActualService;
use App\Services\Enums\KPITypes;
use App\Services\PostVisitKpisService;
use App\Setting;
use App\Shift;
use App\User;
use App\Vacation;
use Carbon\CarbonPeriod;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CallRateReportController extends ApiController
{
    public function __construct(private readonly PostVisitKpisService $postVisitKpisService) {}
    public function lines()
    {
        /**@var User */
        $user = Auth::user();
        $lines = $user->userLines();
        $shifts = Shift::where('deleted_at', null)->orderBy('sort', 'ASC')->get();
        return response()->json([
            'lines' => $lines,
            'shifts' => $shifts
        ]);
    }

    public function filter(Request $request)
    {
        /**@var User $authUser */
        $authUser = Auth::user();
        $visit = $request->visitFilter;
        $from = Carbon::parse($visit['fromDate'])->startOfDay();
        $to = Carbon::parse($visit['toDate'])->endOfDay();
        $month = [Carbon::parse($from)->format('m'), Carbon::parse($to)->format('m')];
        $year = Carbon::parse($from)->format('Y');
        $months = [];
        $period = CarbonPeriod::create($from, '1 month', $to);
        foreach ($period as $date) {
            $months[] = $date->format('F'); // 'F' gives full month name, use 'm' for month number
        }
        $dates[] = [
            'Month' => implode(', ', $months),
            'Year' => $year,
        ];
        $division_type = DivisionType::where('last_level', '=', 1)->first()->id;
        // $users = $line->users()->wherePivot("deleted_at", null)
        //     ->when(!empty($visit['users']), fn ($q) => $q->whereIn("line_users.user_id", $visit['users']));
        $fields = collect(['id', 'line', 'division', 'employee', 'emp_code', 'working_days', 'vacations', 'requests', 'plan_ow', 'actual_ow', 'net_working_days', 'actual_days', 'daily_target', 'monthly_target', 'actual', 'call_rate', 'avg_visits', 'actual_target']);
        $clickable = collect(['vacations', 'requests', 'plan_ow', 'actual_ow', 'actual_days', 'actual']);
        $accountTypes = AccountType::whereNull('deleted_at')->when(!empty($visit['shift']), fn($q) => $q->where("shift_id", $visit['shift']))->get();
        $fields = $fields->merge($accountTypes->pluck('name'));
        $fields = $fields->merge('call_rate_per_type');

        $clickable = $clickable->merge($accountTypes->pluck('name'));
        $filtered = new Collection([]);
        $data = new Collection([]);
        $lines = Line::when(!empty($visit['lines']), fn($q) => $q->whereIntegerInRaw("lines.id", $visit['lines']))->get();
        $lineIds = $lines->pluck('id');
        $days = Setting::where('key', 'fixed_working_days')->value('value');
        $wd = $days > 0 ? $days : 0;
        foreach ($lines as $line) {
            if ($visit['position'] != null) {
                $filtered = User::whereIntegerInRaw("id", $visit['users'])->get();
            } else {
                $users = $line->users($from, $to)->wherePivot("deleted_at", null)
                    ->when(!empty($visit['users']), fn($q) => $q->whereIn("line_users.user_id", $visit['users']))->get();
                $filtered = $filtered->merge($authUser->filterUsers($line, $users, $visit, $from, $to));
            }
        }
        $division_type = DivisionType::where('last_level', '=', 1)->first()->id;
        $filtered->unique('id')->values()->each(function ($user) use ($wd, $division_type, $lineIds, $from, $to, $accountTypes, $data, $visit, $month, $year) {
            $data = $data->push($this->statistics($lineIds, $user, $from, $to, $visit, $accountTypes, $month, $year, $division_type, $wd));
        });
        return response()->json([
            'data' => $data->unique("id")->values(),
            'fields' => $fields,
            'dates' => $dates,
            'clickable' => $clickable
        ]);
    }


    private function statistics($lineIds, $user, $from, $to, $visit, $accountTypes, $month, $year, $division_type, $wd)
    {
        $lines = $user->lines($from, $to)->get()->pluck('id')->toArray();
        $lineFirstId = !empty($lines) ? $lines[0] : null;
        $divisions = $user->divisions($from, $to)->whereIntegerInRaw('line_divisions.line_id', $lineIds);
        $pivotDivision = $divisions->first()?->pivot;
        // throw new CrmException($pivotDivision);
        $userFromDate = $from;
        $userToDate = $to;
        if ($pivotDivision) {
            $userFromDate = $pivotDivision->from_date > $from ? Carbon::parse($pivotDivision->from_date) : $from;
            $userToDate = $pivotDivision->to_date && $pivotDivision->to_date < $to ? Carbon::parse($pivotDivision->to_date) : $to;
        }
        $wd = $this->workingDays($userFromDate, $userToDate, $visit['shift'], $lineFirstId);
        $actuals = ActualVisit::select('id', DB::raw('DATE_FORMAT(crm_actual_visits.visit_date,"%Y-%m-%d") as visit_date'))
            ->where('user_id', $user->id)
            ->whereIntegerInRaw('acc_type_id', $accountTypes->pluck('id')->toArray())
            ->whereBetween('visit_date', [$from, $to])
            ->get();

        $actualDays = $actuals->pluck('visit_date')->unique()->count();
        $actualCounts = $actuals->count();
        $vacs = $this->vacations($user, $from, $to, $month, $year, $visit, $lineFirstId);
        $ow = $this->ow($user, $from, $to, $visit, $lineFirstId);
        $planOw = $this->planOw($user, $from, $to, $visit, $lineFirstId);
        $requests = CommercialRequest::commercialsPerPeriod($user->id, $from, $to, [$visit['shift']]);
        $net_days = $wd - $vacs - $ow - $requests;
        $dailyTarget = 0;
        if (!empty($lines)) {
            $dailyTarget = $this->dailyTarget($lines, $user, $month, $year, $visit['shift'], $from, $to);
        }
        $monthlyTarget = $dailyTarget * $net_days;
        $data = collect([
            'id' => $user->id,
            'line' => $user->lines($from, $to)->whereIntegerInRaw('line_users.line_id', $lineIds)->pluck('name')->implode(','),
            'division' => $divisions->pluck('name')->implode(','),
            'employee' => $user->fullname,
            'emp_code' => $user->emp_code ?? '',
            'working_days' => $wd,
            'vacations' => $vacs,
            'requests' => $requests,
            'plan_ow' => $planOw,
            'actual_ow' => $ow,
            'net_working_days' => $net_days,
            'actual_days' => $actualDays,
            'daily_target' => $dailyTarget,
            'monthly_target' => $monthlyTarget,
            'from' => $from,
            'to' => $to,
            'actual' => $actualCounts,
            'call_rate' => $monthlyTarget ? round(($actualCounts / $monthlyTarget) * 100, 2) . '%' : 0 . '%',
            'avg_visits' => $actualDays > 0 ? round($actualCounts / $actualDays, 0) : 0,
            'actual_target' => round($actualCounts / 18),
            'color' => $user->divisions($from, $to)?->first()?->DivisionType->color,
        ]);
        $accountTypes->each(function ($accountType) use ($user, $lines, $from, $to, $data, $monthlyTarget) {
            $actuals = ActualVisit::where('user_id', $user->id)->where('acc_type_id', $accountType->id)
                ->whereBetween('visit_date', [$from, $to])->get();
            if ($accountType->shift_id == 1) {
                $actuals = $actuals->unique(function ($item) {
                    return $item->visit_date . $item->account_id;
                })->values();
            }
            $countTypeActuals = $actuals->count();
            $data = $data->put($accountType->name, $countTypeActuals);
            $data = $data->put('call_rate_per_type', $monthlyTarget ? round(($countTypeActuals / $monthlyTarget) * 100, 2) . '%' : 0 . '%');
        });
        return $data;
    }

    public function workingDays($from, $to, $shiftId, $line_id)
    {
        return CallRate::calculateWorkingDays($from, $to, $shiftId, $line_id);
    }

    public function vacations($user, $from, $to, $month, $year, $visit, $lineFirstId)
    {

        return Vacation::getVacationsDatePerPeriod(
            user: $user,
            from: $from,
            to: $to,
            month: $month,
            year: $year,
            shifts: [$visit['shift']],
            line_id: $lineFirstId
        );
    }

    public function ow($user, $from, $to, $visit, $line_id)
    {
        $count = 0.0;
        $ow = OwActualVisit::select(
            'ow_actual_visits.id',
            'ow_actual_visits.user_id',
            'ow_actual_visits.date',
            'ow_actual_visits.shift_id',
            'ow_actual_visits.ow_type_id'
        )
            ->leftJoin('office_work_types', 'ow_actual_visits.ow_type_id', 'office_work_types.id')
            ->where('office_work_types.with_deduct', '1')
            ->where('user_id', $user->id)->whereBetween('date', [$from, $to]);
        if ($visit['shift'] != null) {
            // $ow = $ow->where('shift_id', $visit['shift']);
            if ($visit['shift'] == 3) $visit['shift'] = 2;
            $ow = $ow->where(fn($q) => $q->where('shift_id', $visit['shift'])->orWhereNull('shift_id'));
        }
        $ow = $ow->get()->filter(function ($ow) use ($visit, $from, $to, $line_id) {
            return !OffDay::isOffDay($from, $to, $ow->date, $visit['shift'], $line_id);
        })->each(function ($office) use (&$count, $visit) {
            if ($visit['shift']) {
                $count += 1.0;
            } else {
                $count = $office->shift_id == null
                    ? $count += 1.0
                    : $count += 0.5;
            }
        });

        return $count ?? 0;
    }

    public function planOw($user, $from, $to, $visit, $line_id)
    {
        $count = 0.0;
        $ow = OwPlanVisit::where('user_id', $user->id)->whereBetween('day', [$from, $to]);
        if ($visit['shift'] != null) {
            if ($visit['shift'] == 3) $visit['shift'] = 2;
            $ow = $ow->where('shift_id', $visit['shift']);
        }
        $ow = $ow->get()->filter(function ($ow) use ($visit, $from, $to, $line_id) {
            return !OffDay::isOffDay($from, $to, $ow->date, $visit['shift'], $line_id);
        })->each(function ($office) use (&$count, $visit) {
            if ($visit['shift']) {
                $count += 1.0;
            } else {
                $count = $office->shift_id == null
                    ? $count += 1.0
                    : $count += 0.5;
            }
        });

        return $count ?? 0;
    }

    public function dailyTarget(array $lineIds, User $user, $months, $year, $shift, $from, $to): float|int
    {
        $divisionIds = $user
            ->divisions($from, $to)
            ->whereIn("line_divisions.line_id", $lineIds)
            ->get()
            ->pluck("id")
            ->toArray();
        $callRate = CallRate::select(["call_rate"])
            ->whereIn("division_id", $divisionIds)
            ->whereBetween(DB::raw("(DATE_FORMAT(crm_call_rates.date,'%m'))"), $months)
            ->whereYear("call_rates.date", $year);

        if ($shift != null) {
            $callRate->where("shift_id", $shift);
        }

        $rate = $callRate->first()?->call_rate;

        return !!$rate ? $rate : 0;
    }

    public function showData(Request $request)
    {
        $list = $request->listFilter;
        $user = User::where('id', $request->user)->first();
        $column = $request->column;
        $from = Carbon::parse($list['fromDate'])->startOfDay();
        $to = Carbon::parse($list['toDate'])->endOfDay();
        $month = [Carbon::parse($from)->format('m'), Carbon::parse($to)->format('m')];
        $year = Carbon::parse($from)->format('Y');
        $lines = $user->lines($from, $to)->get()->pluck('id')->toArray();
        $accountTypes = AccountType::whereNull('deleted_at');
        if ($list['shift'] != null) {
            $accountTypes = $accountTypes->where('shift_id', $list['shift']);
        }
        $accountTypesId = $accountTypes->get()->pluck('id');
        $accountTypes = $accountTypes->get();
        $division_type = DivisionType::where('last_level', '=', 1)->first()->id;
        $divisions = $user->allBelowDivisions()->whereIn('line_id', $lines)->where('is_kol', 0)
            ->where('division_type_id', $division_type)->unique('id')->pluck('id')->toArray();
        $data = null;

        if ($column == 'vacations') {
            $data = Vacation::select('id', 'user_id', 'vacation_type_id', 'from_date', 'to_date', 'full_day', 'shift_id')
                ->where(fn($q) => $q->whereIn('shift_id', [$list['shift']])->orWhereNull('shift_id'))
                ->where('user_id', $user->id)
                ->where(fn($q) => $q->whereBetween(DB::raw("(DATE_FORMAT(crm_vacations.from_date,'%m'))"), $month)
                    ->orWhereBetween(
                        DB::raw("(DATE_FORMAT(crm_vacations.from_date,'%m'))"),
                        [Carbon::now()->subMonth()->format('m'), Carbon::now()->addYear(1)->format('m')]
                    ))
                ->where(fn($q) => $q->whereYear('vacations.from_date', $year)->orWhereYear('vacations.to_date', $year))
                ->whereHas('details', function ($q) {
                    $q->where('approval', 1);
                })->get()->filter(function ($vacation) use ($from, $to, $list, $lines) {
                    $period = CarbonPeriod::create($vacation->from_date, $vacation->to_date);
                    foreach ($period as $date) {
                        if ($date->between($from, $to)) {
                            if (
                                !OffDay::isOffDay($vacation->from_date, $vacation->to_date, $date, $list['shift'], $lines[0])
                            ) {
                                return [$vacation];
                            }
                        }
                    }
                });
            $data = $data->values()->map(function ($vacation) {
                return [
                    'id' => $vacation->id,
                    'user' => $vacation->user->fullname,
                    'from_date' => Carbon::parse($vacation->from_date)->toDateString(),
                    'to_date' => Carbon::parse($vacation->to_date)->toDateString(),
                    'type' => $vacation->type->name,
                    'shift' => $vacation->shift ? $vacation->shift->name : 'Full Day',
                    'notes' => $vacation->notes ?? '',
                ];
            });
        }
        if ($column == 'requests') {
            $data = CommercialRequest::leftJoin('request_types', 'commercial_requests.request_type_id', 'request_types.id')
                ->where('request_types.with_deduct', '1')->whereBetween('commercial_requests.created_at', [$from, $to])
                ->where('commercial_requests.user_id', $user->id);
            if ($list['shift'] != null) {
                $data = $data->where(fn($q) => $q->where('shift_id', $list['shift'])->orWhereNull('shift_id'));
            }
            $data = $data->get()->map(function ($commercial) {
                return [
                    'id' => $commercial->id,
                    'type' => $commercial->requestType?->name,
                    'date' => Carbon::parse($commercial->created_at)->toDateString(),
                    'user' => $commercial->user->fullname,
                    'shift' => $commercial->shift != null ? $commercial->shift?->name : "Full Day",
                    'description' => $commercial->description ?? "",
                    'amount' => $commercial->amount,

                ];
            });
        }
        if ($column == 'actual_ow') {
            $lineFirstId = !empty($lines) ? $lines[0] : null;
            $data = OwActualVisit::leftJoin('office_work_types', 'ow_actual_visits.ow_type_id', 'office_work_types.id')
                ->where('office_work_types.with_deduct', '1')
                ->where('user_id', $user->id)->whereBetween('date', [$from, $to]);
            if ($list['shift'] != null) {
                $data = $data->where(fn($q) => $q->where('shift_id', $list['shift'])->orWhereNull('shift_id'));
            }
            $data = $data->get()->filter(function ($ow) use ($list, $from, $to, $lineFirstId) {
                return !OffDay::isOffDay($from, $to, $ow->date, $list['shift'], $lineFirstId);
            })->values()->map(function ($officework) {
                return [
                    'id' => $officework->id,
                    'type' => $officework->owType?->name,
                    'date' => Carbon::parse($officework->date)->toDateString(),
                    'user' => $officework->user->fullname,
                    'shift' => $officework->shift != null ? $officework->shift?->name : "Full Day",
                    'notes' => $officework->notes ?? "",
                ];
            });
        }
        if ($column == 'plan_ow') {
            $data = OwPlanVisit::where('user_id', $user->id)->whereBetween('day', [$from, $to]);
            if ($list['shift'] != null) {
                $data = $data->where('shift_id', $list['shift']);
            }
            $data = $data->get()->map(function ($officework) {
                return [
                    'id' => $officework->id,
                    'type' => $officework->owType?->name,
                    'day' => Carbon::parse($officework->day)->toDateString(),
                    'user' => $officework->user->fullname,
                    'shift' => $officework->shift != null ? $officework->shift?->name : "Full Day",
                    'notes' => $officework->notes ?? "",
                ];
            });
        }
        if ($column == 'actual') {
            // $doctors = (new DoctorService)->getDoctorsِCoverage($lines, $divisions, $from, $to);
            // $doctorIds = $doctors->pluck('id')->toArray();
            $data = ActualVisit::where('user_id', $user->id)
                // ->whereIntegerInRaw('line_id', $lines)
                // ->whereIntegerInRaw('account_dr_id', $doctorIds)
                ->whereIntegerInRaw('acc_type_id', $accountTypesId)
                ->whereBetween('visit_date', [$from, $to])
                ->get()
                ->map(function ($actual) {
                    return [
                        'id' => $actual->id,
                        'date' => $actual->visit_date,
                        'user' => $actual->user->fullname,
                        'type' => $actual->visitType->name,
                        'line' => $actual->line->name,
                        'division' => $actual->division->name,
                        'account' => $actual->account?->name,
                        'account_id' => $actual->account?->id,
                        'doctor' => $actual->doctor != null ? $actual->doctor->name : "",
                        'doctor_id' => $actual->doctor != null ? $actual->doctor->id : "",
                        'speciality' => $actual->doctor != null ? $actual->doctor->speciality->name : "",
                        'acc_type' => $actual->account?->type != null ? $actual->account?->type?->name : "",
                        'shift' => $actual->shift != null ? $actual->shift?->name : "",
                    ];
                });
        }
        if ($column == 'actual_days') {
            $data = (new ActualService)->getActuals($user, 'users.id', $from, $to, [$list['shift'], $lines])->unique('date')
                ->values()->map(function ($actual) {
                    return [
                        'id' => $actual->id,
                        'date' => $actual->date,
                        'user' => $actual->employee,
                        'type' => $actual->type,
                        'line' => $actual->line,
                        'division' => $actual->division,
                        'account' => $actual->account,
                        'account_id' => $actual->account_id,
                        'doctor' => $actual->doctor != null ? $actual->doctor : "",
                        'doctor_id' => $actual->doctor != null ? $actual->doctor_id : "",
                        'speciality' => $actual->doctor != null ? $actual->speciality : "",
                        'acc_type' => $actual->acc_type != null ? $actual->acc_type : "",
                        'shift' => $actual->shift != null ? $actual->shift : "",
                    ];
                });
        }
        foreach ($accountTypes as $accountType) {
            if ($column == $accountType->name) {
                $data = ActualVisit::where('user_id', $user->id)
                    ->where('acc_type_id', $accountType->id)
                    ->whereIntegerInRaw('line_id', $lines)
                    ->whereBetween('visit_date', [$from, $to])
                    ->get()
                    ->map(function ($actual) {
                        return [
                            'id' => $actual->id,
                            'date' => $actual->visit_date,
                            'user' => $actual->user->fullname,
                            'type' => $actual->visitType->name,
                            'line' => $actual->line->name,
                            'division' => $actual->division->name,
                            'account' => $actual->account?->name,
                            'account_id' => $actual->account?->id,
                            'doctor' => $actual->doctor != null ? $actual->doctor->name : "",
                            'doctor_id' => $actual->doctor != null ? $actual->doctor->id : "",
                            'speciality' => $actual->doctor != null ? $actual->doctor->speciality->name : "",
                            'acc_type' => $actual->account?->type != null ? $actual->account?->type?->name : "",
                            'shift' => $actual->shift != null ? $actual->shift?->name : "",
                        ];
                    });
            }
        }


        return $this->respond($data);
    }
    public function post(Request $request)
    {
        $callrateData = $request->data;
        $filters = $request->filters;
        $from = Carbon::parse($filters['fromDate'])->startOfMonth()->toDateString();
        $to = Carbon::parse($filters['toDate'])->endOfMonth()->toDateString();
        $period = CarbonPeriod::create($from, '1 month', $to);

        $shift = Shift::find($filters['shift']);


        $kpiName = $shift->name . ' Call Rate';
        $kpi = Kpi::where('name', $kpiName)->first();

        $shiftFieldMap = [
            1 => ['key' => 'am_call_rate', 'type' => KPITypes::AM_CALL_RATE],
            2 => ['key' => 'pm_call_rate', 'type' => KPITypes::PM_CALL_RATE],
            3 => ['key' => 'ph_call_rate', 'type' => KPITypes::PH_CALL_RATE],
        ];

        if (!isset($shiftFieldMap[$shift->id])) {
            return $this->respondWithError('Unsupported shift ID.');
        }

        $field = $shiftFieldMap[$shift->id]['key'];
        $kpiType = $shiftFieldMap[$shift->id]['type'];

        $formattedData = array_map(function ($item) use ($field) {
            $value = floatval(str_replace('%', '', $item['call_rate']));
            return [
                'id' => $item['id'],
                $field => round($value, 2),
            ];
        }, $callrateData);

        return $this->handlePost($kpiType, $formattedData, $period, $kpi);
    }
    private function handlePost($kpiType, $data, $period, $kpi)
    {
        $this->postVisitKpisService->post($kpiType, $data, $period, $kpi);
        return $this->respondSuccess();
    }
}
