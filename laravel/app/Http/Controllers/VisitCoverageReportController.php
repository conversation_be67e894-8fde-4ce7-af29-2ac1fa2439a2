<?php

namespace App\Http\Controllers;

use App\Account;
use App\AccountType;
use App\ActualVisit;
use App\DivisionType;
use App\Doctor;
use App\Exceptions\CrmException;
use App\Http\Controllers\Controller;
use App\Line;
use App\LineDivision;
use App\Models\Kpi;
use App\Models\NewAccountDoctor;
use App\Models\PostVisitKpi;
use App\Services\AccountService;
use App\Services\ActualService;
use App\Services\DoctorService;
use App\Services\Enums\KPITypes;
use App\Services\PostVisitKpisService;
use App\Shift;
use App\User;
use Carbon\CarbonPeriod;
use COM;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class VisitCoverageReportController extends ApiController
{
    public function __construct(private readonly PostVisitKpisService $postVisitKpisService) {}
    public function filter(Request $request)
    {
        /**@var User authUser */
        $authUser = Auth::user();
        $visit = $request->visitFilter;
        $from = Carbon::parse($visit['fromDate'])->startOfDay();
        $to = Carbon::parse($visit['toDate'])->endOfDay();

        $months = [];
        $period = CarbonPeriod::create($from, '1 month', $to);
        foreach ($period as $date) {
            $months[] = $date->format('F'); // 'F' gives full month name, use 'm' for month number
        }
        $dates[] = [
            'Month' => implode(', ', $months),
            'Year' => $from->format('Y'),
        ];
        $types = AccountType::whereIn('id', $visit['accountTypes'])->get()->pluck('name');
        $lines = Line::when(!empty($visit['lines']), fn($q) => $q->whereIntegerInRaw("lines.id", $visit['lines']))->get();
        $shifts = Shift::when(!empty($visit['shifts']), fn($q) => $q->whereIntegerInRaw("shifts.id", $visit['shifts']))
            ->get()->pluck('id')->toArray();
        $lineIds = $lines->pluck('id');
        $division_type = DivisionType::where('last_level', '=', 1)->value('id');
        $filtered = new Collection([]);
        $data = new Collection([]);
        foreach ($lines as $line) {
            if ($visit['filter'] == 1) {
                $divisions = $line->divisions($from, $to)->when(!empty($visit['divisions']), fn($q) => $q->whereIntegerInRaw("line_divisions.id", $visit['divisions']))->get();
                $filtered = $filtered->merge($authUser->filterDivisions($line, $divisions, $visit, $from, $to));
            }
            if ($visit['filter'] == 2) {
                $users = $line->users($from, $to)
                    ->when(!empty($visit['users']), fn($q) => $q->whereIntegerInRaw("line_users.user_id", $visit['users']))->get();
                $filtered = $filtered->merge($authUser->filterUsers($line, $users, $visit, $from, $to));
            }
        }
        $filtered->unique('id')->values()->each(function ($object) use ($lineIds, $data, $visit, $from, $to, $division_type, $shifts) {
            $data->push($this->statistics($lineIds, $object, $from, $to, $visit, $division_type, $shifts));
        });
        return response()->json(['data' => $data->unique("id")->values(), 'types' => $types, 'dates' => $dates]);
    }

    private function statistics($lineIds, $object, $from, $to, $visit, $division_type, $shifts)
    {

        $lines = collect([]);
        $divisions = [];
        $below = collect([]);
        $filterdDivisions = collect([]);
        if ($visit['filter'] == 2) {
            $lines = $visit['result_by'] == 'filtered'
                ?  $object->lines($from, $to)->whereIntegerInRaw('lines.id', $visit['lines'])->get()->pluck('id')->toArray()
                : $object->lines($from, $to)->get()->pluck('id')->toArray();

            // if ($object->is_manager) {
            $divisions = $object->allBelowDivisions(from: $from, to: $to)->whereIn('line_id', $lines)->where('is_kol', 0)
                ->where('division_type_id', $division_type)->unique('id')->pluck('id')->toArray();
            collect($divisions)->each(function ($division) use ($filterdDivisions, $below, $lines, $from, $to) {
                $div = LineDivision::find($division);
                $divWithUser = $div->users($from, $to)->whereIntegerInRaw('line_id', $lines)->get();
                if (count($divWithUser) != 0) {
                    $below = $below->push($divWithUser);
                    $filterdDivisions = $filterdDivisions->push($div);
                }
            });
            $filterdDivisions = $filterdDivisions->pluck('id')->unique()->values()->toArray();
            $below = $below->collapse()->pluck('id')->unique()->values()->toArray();
            // } else {
            // $filterdDivisions = $object->divisions($from, $to)->pluck('line_divisions.id')->unique()->values()->toArray();
            // $below = [$object->id];
            // }
        } else {
            $lines = $object->line()->get()->pluck('id')->toArray();
            if ($object->is_kol) {
                $filterdDivisions = [$object->id];
            } else {
                $filterdDivisions = $object->getBelowDivisions($from, $to)
                    ->where('division_type_id', '=', $division_type)->where('is_kol', 0)->unique('id')->pluck('id')->toArray();
            }

            $below = $filterdDivisions;
        }

        $count = [];
        $data = [
            'coveredAccounts' => 0,
            'coveredDoctors' => 0,
            'unCoveredAccounts' => 0,
            'unCoveredDoctors' => 0,
            'accountCoverage' => 0,
            'doctorCoverage' => 0,
        ];
        $actuals = 0;
        if (!empty($filterdDivisions)) {
            $count = $this->countAccountsAndDoctors($lines, $visit, $filterdDivisions, $from, $to, $shifts);
            $data = $this->coveredAccountsAndDoctors($lines, $object, $from, $to, $visit, $count, $below);
            $actuals = $visit['filter'] == 2 ? (new ActualService)->teamActuals(
                from: $from,
                to: $to,
                users: $below,
                table: 'users.id',
                lines: $lines,
                shifts: $shifts,
            )->count() : (new ActualService)->teamActuals(
                from: $from,
                to: $to,
                users: $below,
                table: 'line_divisions.id',
                lines: $lines,
                shifts: $shifts
            )->count();
        }

        if ($visit['type'] == 1)
            return [
                'id' => $object->id,
                'line' => $visit['filter'] == 1 ? $object?->line?->name : $object->lines($from, $to)->whereIntegerInRaw('line_users.line_id', $lineIds)->pluck('name')->implode(','),
                'division' => $visit['filter'] == 1 ? $object?->name : $object->divisions($from, $to)->whereIntegerInRaw('line_divisions.line_id', $lineIds)->pluck('name')->implode(','),
                'employee' => $visit['filter'] == 1 ? $object->users($from, $to)?->whereIntegerInRaw('line_users_divisions.line_id', $lineIds)->pluck('fullname')->implode(',') : $object?->fullname,
                'emp_code' => $visit['filter'] == 1 ? ($object->users($from, $to)?->whereIntegerInRaw('line_users_divisions.line_id', $lineIds)->pluck('emp_code')->implode(',') ?? '') : $object?->emp_code ?? '',
                'color' => $visit['filter'] == 1 ? $object?->DivisionType->color : $object->divisions($from, $to)?->first()?->DivisionType->color,
                'num_accounts' => $count['countAccounts'] ?? 0,
                'covered_acc' => $data['coveredAccounts'] ?? 0,
                'uncovered_acc' => $data['unCoveredAccounts'] ?? 0,
                'coverage' => $data['accountCoverage'] ?? 0,
                'actual_frequency' => $data['coveredAccounts'] > 0 ? round($actuals / $data['coveredAccounts'], 2) : 0,
            ];
        if ($visit['type'] == 2) {
            return [
                'id' => $object->id,
                'line' => $visit['filter'] == 1 ? $object?->line?->name : $object->lines($from, $to)->whereIntegerInRaw('line_users.line_id', $lineIds)->pluck('name')->implode(','),
                'division' => $visit['filter'] == 1 ? $object?->name : $object->divisions($from, $to)->whereIntegerInRaw('line_divisions.line_id', $lineIds)->pluck('name')->implode(','),
                'employee' => $visit['filter'] == 1 ? $object->users($from, $to)?->whereIntegerInRaw('line_users_divisions.line_id', $lineIds)->pluck('fullname')->implode(',') : $object?->fullname,
                'emp_code' => $visit['filter'] == 1 ? ($object->users($from, $to)?->whereIntegerInRaw('line_users_divisions.line_id', $lineIds)->pluck('emp_code')->implode(',') ?? '') : $object?->emp_code ?? '',
                'color' => $visit['filter'] == 1 ? $object?->DivisionType->color : $object->divisions($from, $to)?->first()?->DivisionType->color,
                'num_doctors' => $count['countDoctors'] ?? 0,
                'covered_doc' => $data['coveredDoctors'] ?? 0,
                'uncovered_doc' => $data['unCoveredDoctors'] ?? 0,
                'coverage' => $data['doctorCoverage'] ?? 0,
                'actual_frequency' => $data['coveredDoctors'] > 0 ? round($actuals / $data['coveredDoctors']) : 0,
            ];
        }
    }

    public function countAccountsAndDoctors($lines, $visit, $divisions, $from, $to, $shifts)
    {
        // throw new CrmException($divisions);
        $accounts = collect([]);
        $doctors = collect([]);
        if ($visit['type'] == 1) {
            $accounts = (new AccountService)->getAccountsCoverage(
                lines: $lines,
                divisions: $divisions,
                from: $from,
                to: $to,
                specialities: $visit['specialities'],
                accountTypes: $visit['accountTypes'],
                classes: $visit['classes'],
                shifts: $shifts
            );
        } else {
            $doctors = (new DoctorService)->getDoctorsِCoverage(
                lines: $lines,
                divisions: $divisions,
                from: $from,
                to: $to,
                specialities: $visit['specialities'],
                accountTypes: $visit['accountTypes'],
                classes: $visit['classes'],
                shifts: $shifts
            );
        }
        $countDoctors = $doctors->count();
        $countAccounts = $accounts->count();

        return array(
            'accounts' => $accounts,
            'countAccounts' => $countAccounts,
            'doctors' => $doctors,
            'countDoctors' => $countDoctors
        );
    }

    public function countAccountsAndDoctorsForShowData($lines, $visit, $divisions, $from, $to, $shifts)
    {
        $accounts = 0;
        $doctors = 0;
        $accounts = (new AccountService)->getAccountsCoverage(
            lines: $lines,
            divisions: $divisions,
            from: $from,
            to: $to,
            specialities: $visit['specialities'],
            accountTypes: $visit['accountTypes'],
            classes: $visit['classes'],
            shifts: $shifts
        );
        $countAccounts = $accounts->count();
        $doctors = (new DoctorService)->getDoctorsِCoverage(
            lines: $lines,
            divisions: $divisions,
            from: $from,
            to: $to,
            specialities: $visit['specialities'],
            accountTypes: $visit['accountTypes'],
            classes: $visit['classes'],
            shifts: $shifts
        );
        $countDoctors = $doctors->count();

        return array(
            'accounts' => $accounts,
            'countAccounts' => $countAccounts,
            'doctors' => $doctors,
            'countDoctors' => $countDoctors
        );
    }

    public function coveredAccountsAndDoctors($lines, $object, $from, $to, $visit, $count, $below)
    {
        $allAccounts = $count['accounts'];
        $allDoctors = $count['doctors'];
        $condition = '';
        if ($visit['filter'] == 1) {
            $condition = 'div_id';
        }
        if ($visit['filter'] == 2) {
            $condition = 'user_id';
        }
        $visitedAcc = collect([]);
        $visitedDoc = collect([]);
        foreach ($below as $belowObject) {
            if ($visit['type'] == 1) {
                $belowObjectVisits = ActualVisit::whereIntegerInRaw('line_id', $lines)->where($condition, $belowObject)
                    ->whereIntegerInRaw('account_id', $allAccounts->pluck('account_id'))
                    ->whereBetween('visit_date', [$from, $to]);
                if (!empty($visit['products'])) {
                    $belowObjectVisits->whereHas('actualVisitProducts', fn($q) => $q->whereIntegerInRaw('product_id', $visit['products']));
                }
                $belowObjectVisits = $belowObjectVisits->get()->unique('account_id')->values();
                $visitedAcc = $visitedAcc->push($belowObjectVisits);
            } else {
                $belowObjectVisits = ActualVisit::whereIntegerInRaw('line_id', $lines)->where($condition, $belowObject)
                    ->whereIntegerInRaw('account_dr_id', $allDoctors->pluck('doctor_id'))
                    ->whereBetween('visit_date', [$from, $to]);
                if (!empty($visit['products'])) {
                    $belowObjectVisits->whereHas('actualVisitProducts', fn($q) => $q->whereIntegerInRaw('product_id', $visit['products']));
                }
                $belowObjectVisits = $belowObjectVisits->get()->unique('account_dr_id')->values();
                $visitedDoc = $visitedDoc->push($belowObjectVisits);
            }
        }
        $visitedAccounts = $visitedAcc->collapse()->pluck('account_id')->count();
        $visitedDoctors = $visitedDoc->collapse()->pluck('account_dr_id')->count();

        $uncoverdAccounts = count($allAccounts) - $visitedAccounts;
        $uncoverdDoctors = count($allDoctors) - $visitedDoctors;
        $accountCoverage = count($allAccounts) ? round(($visitedAccounts / count($allAccounts)) * 100, 2) . '%' : 0 . '%';
        $doctorCoverage = count($allDoctors) ? round(($visitedDoctors / count($allDoctors)) * 100, 2) . '%' : 0 . '%';


        return array(
            'coveredAccounts' => $visitedAccounts,
            'coveredDoctors' => $visitedDoctors,
            'unCoveredAccounts' => $uncoverdAccounts,
            'unCoveredDoctors' => $uncoverdDoctors,
            'accountCoverage' => $accountCoverage,
            'doctorCoverage' => $doctorCoverage,
        );
    }

    public function showData(Request $request)
    {
        $visit = $request->listFilter;
        $lines = Line::when(!empty($visit['lines']), fn($q) => $q->whereIntegerInRaw("lines.id", $visit['lines']))->get();
        $shifts = Shift::when(!empty($visit['shifts']), fn($q) => $q->whereIntegerInRaw("shifts.id", $visit['shifts']))
            ->get()->pluck('id')->toArray();
        $column = $request->column;
        $fromDate = Carbon::parse($visit['fromDate'])->startOfDay();
        $toDate = Carbon::parse($visit['toDate'])->endOfDay();
        $condition = null;
        $object = null;
        $data = collect([]);
        $division_type = DivisionType::where('last_level', '=', 1)->value('id');
        $lines = [];
        $divisions = [];
        $belows = collect([]);
        $filteredDivisions = collect([]);
        if ($visit['filter'] == 1) {
            $object = LineDivision::find($request->div);
            $condition = 'div_id';
            $lines = $object->line()->get()->pluck('id')->toArray();
            if ($object->is_kol) {
                $filtereDivisions = [$object->id];
            } else {
                $filtereDivisions = $object->getBelowDivisions()
                    ->where('division_type_id', '=', $division_type)->where('is_kol', 0)->unique('id')->pluck('id')->toArray();
            }
            $data = $this->countAccountsAndDoctorsForShowData($lines, $visit, $filtereDivisions, $fromDate, $toDate, $shifts);
            $belows = $filtereDivisions;
        }
        if ($visit['filter'] == 2) {
            $object = User::find($request->user);
            $condition = 'user_id';
            $lines = collect([]);
            if ($visit['result_by'] == 'filtered') {
                $lines = $object->lines($fromDate, $toDate)->whereIntegerInRaw('lines.id', $visit['lines'])->get()->pluck('id')->toArray();
            } else {
                $lines = $object->lines($fromDate, $toDate)->get()->pluck('id')->toArray();
            }
            $divisions = $object->allBelowDivisions(from: $fromDate, to: $toDate)->whereIn('line_id', $lines)->where('is_kol', 0)
                ->where('division_type_id', $division_type)->unique('id')->pluck('id')->toArray();
            collect($divisions)->each(function ($division) use ($filteredDivisions, $belows, $lines, $fromDate, $toDate) {
                $div = LineDivision::find($division);
                $divWithUser = $div->users($fromDate, $toDate)->whereIntegerInRaw('line_id', $lines)->get();
                if (count($divWithUser) != 0) {
                    $belows = $belows->push($divWithUser);
                    $filteredDivisions = $filteredDivisions->push($div);
                }
            });
            $filteredDivisions = $filteredDivisions->pluck('id')->unique()->values()->toArray();
            $belows = $belows->collapse()->pluck('id')->unique()->values()->toArray();
            $data = $this->countAccountsAndDoctorsForShowData($lines, $visit, $filteredDivisions, $fromDate, $toDate, $shifts);
        }




        $accounts = $data['accounts'];
        $doctors = $data['doctors'];
        $coveredAccounts = collect([]);
        $unCoveredAccounts = collect([]);
        $coveredDoctors = collect([]);
        $unCoveredDoctors = collect([]);

        foreach ($belows as $below) {
            $visitAccounts = ActualVisit::whereIntegerInRaw('line_id', $lines)
                ->whereIntegerInRaw('account_id', $accounts->pluck('account_id'))
                ->where($condition, $below)->whereBetween('visit_date', [$fromDate, $toDate]);
            if (!empty($visit['products'])) {
                $visitAccounts->whereHas('actualVisitProducts', fn($q) => $q->whereIntegerInRaw('product_id', $visit['products']));
            }
            $visitAccounts = $visitAccounts->get()->unique('account_id')->values();
            $coveredAccounts = $coveredAccounts->push($accounts->whereIn('account_id', $visitAccounts->pluck('account_id'))->unique());
            $visitDoctors = ActualVisit::whereIntegerInRaw('line_id', $lines)
                ->whereIntegerInRaw('account_dr_id', $doctors->pluck('doctor_id'))
                ->where($condition, $below)->whereBetween('visit_date', [$fromDate, $toDate]);
            if (!empty($visit['products'])) {
                $visitDoctors->whereHas('actualVisitProducts', fn($q) => $q->whereIntegerInRaw('product_id', $visit['products']));
            }
            $visitDoctors = $visitDoctors->get()->unique('account_dr_id')->values();
            $coveredDoctors = $coveredDoctors->push($doctors->whereIn('doctor_id', $visitDoctors
                ->pluck('account_dr_id'))->unique());
        }
        if ($column == 'num_accounts') {
            return $this->respond($accounts->map(function ($account) use ($fromDate, $toDate, $object, $visit) {

                // $accountDoctor = NewAccountDoctor::where('account_id', $account->account_id)->where('doctor_id', $account->doctor_id)
                //     ->where('new_account_doctors.from_date', '<=', $toDate)
                //     ->where(fn($q) => $q->where('new_account_doctors.to_date', '=', null)->orWhereDate('new_account_doctors.to_date', '>', '2023-12-19 00:00:00'))
                //     ->first();
                $accountVisits = ActualVisit::where('account_id', $account->account_id)
                    ->whereBetween('visit_date', [$fromDate, $toDate]);
                if (!empty($visit['products'])) {
                    $accountVisits->whereHas('actualVisitProducts', fn($q) => $q->whereIntegerInRaw('product_id', $visit['products']));
                }
                return [
                    'line' => $account->line,
                    'division' => $account->division,
                    'brick' => $account->brick,
                    'account' => $account->account,
                    'account_id' => $account->account_id,
                    'doctor' => $account->doctor,
                    'doctor_id' => $account->doctor_id,
                    'type' => $account->account_type,
                    'speciality' => $account->speciality,
                    // 'from_date' => Carbon::parse($accountDoctor?->from_date)->toDateString(),
                    // 'to_date' => Carbon::parse($accountDoctor?->to_date)->toDateString(),
                    'no_of_visits' => $accountVisits->count(),
                    'visit_date' => $accountVisits->pluck('visit_date')->unique()->implode(' , '),
                    'acc_visitors' => $accountVisits->get()->unique('user_id')
                        ->pluck('user.fullname')
                        ->implode(', ')
                ];
            }));
        }

        if ($column == 'covered_acc') {
            $coveredAccounts = $coveredAccounts->collapse()->map(function ($account) use ($fromDate, $toDate, $object, $condition, $visit) {
                $accountVisits = ActualVisit::where('account_id', $account->account_id)
                    ->where($condition, $object->id)
                    ->whereBetween('visit_date', [$fromDate, $toDate]);
                if (!empty($visit['products'])) {
                    $accountVisits->whereHas('actualVisitProducts', fn($q) => $q->whereIntegerInRaw('product_id', $visit['products']));
                }
                return [
                    'line' => $account->line,
                    'division' => $account->division,
                    'brick' => $account->brick,
                    'account' => $account->account,
                    'account_id' => $account->account_id,
                    'doctor' => $account->doctor,
                    'doctor_id' => $account->doctor_id,
                    'type' => $account->account_type,
                    'speciality' => $account->speciality,
                    'no_of_visits' => $accountVisits->count(),
                    'visit_date' => $accountVisits->pluck('visit_date')->implode(' , ')
                ];
            });
            return $this->respond($coveredAccounts);
        }

        if ($column == 'uncovered_acc') {
            $unCoveredAccounts = $accounts->whereNotIn('account_id', $coveredAccounts->collapse()->pluck('account_id'));
            $unCoveredAccounts = $unCoveredAccounts->values()->map(function ($account) use ($fromDate, $toDate, $object, $condition) {
                return [
                    'line' => $account->line,
                    'division' => $account->division,
                    'brick' => $account->brick,
                    'account' => $account->account,
                    'code' => $account->code,
                    'account_id' => $account->account_id,
                    'doctor' => $account->doctor,
                    'ucode' => $account->ucode,
                    'doctor_id' => $account->doctor_id,
                    'active_date' => $account->active_date,
                    'type' => $account->account_type,
                    'speciality' => $account->speciality,
                ];
            });
            return $this->respond($unCoveredAccounts);
        }

        if ($column == 'num_doctors') {
            return $this->respond($doctors->map(function ($doctor) use ($fromDate, $toDate, $visit, $condition) {
                $doctorVisits = ActualVisit::where('account_dr_id', $doctor->doctor_id)
                    ->whereBetween('visit_date', [$fromDate, $toDate]);
                if (!empty($visit['products'])) {
                    $doctorVisits->whereHas('actualVisitProducts', fn($q) => $q->whereIntegerInRaw('product_id', $visit['products']));
                }
                return [
                    'line' => $doctor->line,
                    'division' => $doctor->division,
                    'brick' => $doctor->brick,
                    'account' => $doctor->account,
                    'account_id' => $doctor->account_id,
                    'doctor' => $doctor->doctor,
                    'doctor_id' => $doctor->doctor_id,
                    'type' => $doctor->account_type,
                    'speciality' => $doctor->speciality,
                    'no_of_visits' => $doctorVisits->count(),
                    'visit_date' => $doctorVisits->pluck('visit_date')->unique()->implode(' , '),
                    'doc_visitors' => $doctorVisits->get()->unique('user_id')->pluck('user.fullname')
                        ->implode(', ')
                ];
            }));
        }
        if ($column == 'covered_doc') {
            $coveredDoctors = $coveredDoctors->collapse()->map(function ($doctor) use ($fromDate, $toDate, $object, $condition, $visit) {
                $doctorVisits = ActualVisit::where('account_dr_id', $doctor->doctor_id)
                    ->where($condition, $object->id)
                    ->whereBetween('visit_date', [$fromDate, $toDate]);
                if (!empty($visit['products'])) {
                    $doctorVisits->whereHas('actualVisitProducts', fn($q) => $q->whereIntegerInRaw('product_id', $visit['products']));
                }
                return [
                    'line' => $doctor->line,
                    'division' => $doctor->division,
                    'brick' => $doctor->brick,
                    'account' => $doctor->account,
                    'account_id' => $doctor->account_id,
                    'doctor' => $doctor->doctor,
                    'doctor_id' => $doctor->doctor_id,
                    'type' => $doctor->account_type,
                    'speciality' => $doctor->speciality,
                    'no_of_visits' => $doctorVisits->count(),
                    'visit_date' => $doctorVisits->pluck('visit_date')->implode(' , '),
                ];
            });
            return $this->respond($coveredDoctors);
        }
        if ($column == 'uncovered_doc') {
            $unCoveredDoctors = $doctors->whereNotIn('doctor_id', $coveredDoctors->collapse()->pluck('doctor_id'));
            $unCoveredDoctors = $unCoveredDoctors->unique('doctor_id')->values()->map(function ($doctor) use ($visit, $fromDate, $toDate, $object, $condition) {
                $doctorVisits = ActualVisit::where('account_dr_id', $doctor->doctor_id)
                    ->where($condition, $object->id)
                    ->whereBetween('visit_date', [$fromDate, $toDate]);
                if (!empty($visit['products'])) {
                    $doctorVisits->whereHas('actualVisitProducts', fn($q) => $q->whereIntegerInRaw('product_id', $visit['products']));
                }
                return [
                    'line' => $doctor->line,
                    'division' => $doctor->division,
                    'brick' => $doctor->brick,
                    'account' => $doctor->account,
                    'account_id' => $doctor->account_id,
                    'doctor' => $doctor->doctor,
                    'doctor_id' => $doctor->doctor_id,
                    'type' => $doctor->account_type,
                    'speciality' => $doctor->speciality,
                    'no_of_visits' => $doctorVisits->count(),
                    'visit_date' => $doctorVisits->pluck('visit_date')->implode(' , '),
                ];
            });

            return $this->respond($unCoveredDoctors);
        }
    }

    // public function post(Request $request)
    // {
    //     $coverageData = $request->data;
    //     $filters = $request->filters;
    //     $kpi = null;
    //     $from = Carbon::parse($filters['fromDate'])->startOfMonth()->toDateString();
    //     $to = Carbon::parse($filters['toDate'])->endOfMonth()->toDateString();
    //     $period = CarbonPeriod::create($from, '1 month', $to);
    //     $shiftsCount = count($filters['shifts']);
    //     if ($shiftsCount == 1) {
    //         $shift = Shift::find($filters['shifts'][0]);
    //         $kpi = Kpi::where('name', $shift->name . ' Coverage')->first();
    //         $shiftData = [];
    //         if ($shift->id == 1) {
    //             foreach ($coverageData as $item) {
    //                 $am = floatval(str_replace('%', '', $item['coverage']));

    //                 $shiftData[] = [
    //                     'id' => $item['id'],
    //                     'am_coverage' => round($am, 2)
    //                 ];
    //             }
    //             // throw new CrmException($shiftData);
    //             $this->postVisitKpisService->post(KPITypes::AM_COVERAGE, $shiftData, $period, $kpi);
    //         }
    //         if ($shift->id == 2) {
    //             foreach ($coverageData as $item) {
    //                 $pm = floatval(str_replace('%', '', $item['coverage']));

    //                 $shiftData[] = [
    //                     'id' => $item['id'],
    //                     'pm_coverage' => round($pm, 2)
    //                 ];
    //             }
    //             $this->postVisitKpisService->post(KPITypes::PM_COVERAGE, $shiftData, $period, $kpi);
    //         }
    //         if ($shift->id == 3) {
    //             foreach ($coverageData as $item) {
    //                 $ph = floatval(str_replace('%', '', $item['coverage']));

    //                 $shiftData[] = [
    //                     'id' => $item['id'],
    //                     'ph_coverage' => round($ph, 2)
    //                 ];
    //             }
    //             $this->postVisitKpisService->post(KPITypes::PH_COVERAGE, $shiftData, $period, $kpi);
    //         }
    //     }
    //     if ($shiftsCount > 1 || $shiftsCount == 0) {
    //         $kpi = Kpi::where('name', 'Coverage')->first();
    //         $this->postVisitKpisService->post(KPITypes::COVERAGE, $coverageData, $period, $kpi);
    //     }

    //     return $this->respondSuccess();
    // }
    public function post(Request $request)
    {
        $coverageData = $request->data;
        $filters = $request->filters;

        $from = Carbon::parse($filters['fromDate'])->startOfMonth()->toDateString();
        $to = Carbon::parse($filters['toDate'])->endOfMonth()->toDateString();
        $period = CarbonPeriod::create($from, '1 month', $to);
        $shifts = $filters['shifts'] ?? [];
        $shiftsCount = count($shifts);

        // Handle case with multiple or zero shifts
        if ($shiftsCount !== 1) {
            $kpi = Kpi::where('name', 'Coverage')->first();
            return $this->handlePost(KPITypes::COVERAGE, $coverageData, $period, $kpi);
        }

        // Handle single shift case
        $shift = Shift::find($shifts[0]);
        if (!$shift) {
            return $this->respondWithError('Invalid shift selected.');
        }

        $kpiName = $shift->name . ' Coverage';
        $kpi = Kpi::where('name', $kpiName)->first();

        $shiftFieldMap = [
            1 => ['key' => 'am_coverage', 'type' => KPITypes::AM_COVERAGE],
            2 => ['key' => 'pm_coverage', 'type' => KPITypes::PM_COVERAGE],
            3 => ['key' => 'ph_coverage', 'type' => KPITypes::PH_COVERAGE],
        ];

        if (!isset($shiftFieldMap[$shift->id])) {
            return $this->respondWithError('Unsupported shift ID.');
        }

        $field = $shiftFieldMap[$shift->id]['key'];
        $kpiType = $shiftFieldMap[$shift->id]['type'];

        $formattedData = array_map(function ($item) use ($field) {
            $value = floatval(str_replace('%', '', $item['coverage']));
            return [
                'id' => $item['id'],
                $field => round($value, 2),
            ];
        }, $coverageData);

        return $this->handlePost($kpiType, $formattedData, $period, $kpi);
    }

    private function handlePost($kpiType, $data, $period, $kpi)
    {
        $this->postVisitKpisService->post($kpiType, $data, $period, $kpi);
        return $this->respondSuccess();
    }
}
