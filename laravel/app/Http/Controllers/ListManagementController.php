<?php

namespace App\Http\Controllers;

use App\Account;
use App\AccountLines;
use App\AccountType;
use App\Brick;
use App\Classes;
use App\DivisionType;
use App\Doctor;
use App\Exceptions\CrmException;
use App\Line;
use App\LineBricks;
use App\LineDivision;
use App\Models\NewAccountDoctor;
use App\Services\ListService;
use App\Speciality;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class ListManagementController extends ApiController
{
    public function index() {}

    public function getAccounts(Request $request)
    {
        /**@var User authUser */
        $authUser = Auth::user();
        $listFilter = $request->listFilter;
        $line = Line::find($listFilter['line']);
        $filtered = new Collection();
        $result = new Collection();
        $data = new Collection();
        $division_type = DivisionType::where('last_level', '=', 1)->first()->id;
        $divisions = $line->divisions()->where("deleted_at", null)
            ->when(!empty($listFilter['divisions']), fn($q) => $q->whereIntegerInRaw("line_divisions.id", $listFilter['divisions']))->get();
        $filtered = $filtered->merge($authUser->filterDivisions($line, $divisions, $listFilter));
        $filtered->each(function ($division) use ($result, $division_type) {
            $result = $result->push($division?->getBelowDivisions()
                ->where('division_type_id', '=', $division_type)->where('is_kol', 0));
        });

        $result = $result->collapse()->pluck('id')->unique()->toArray();
        $accounts = collect([]);
        if ($listFilter['filter'] == 'All') {
            $accounts = $this->all($listFilter, $result);
        }
        if ($listFilter['filter'] == 'Active') {
            $accounts = $this->active($listFilter, $result);
        }
        if ($listFilter['filter'] == 'Inactive') {
            $accounts = $this->inActive($listFilter, $result);
        }
        if (!$listFilter['filter']) {
            $accounts = $this->active($listFilter, $result);
        }
        $fields = collect([
            "s",
            "account_id",
            "line",
            "division",
            "brick",
            "account",
            "acc_class",
            "account_type",
            "acc_mobile",
            "address",
            "doctor",
            "doc_class",
            "speciality",
            "doc_mobile",
            "from_date",
            "to_date",
        ]);
        return $this->respond(compact('accounts', 'fields'));
    }

    public function all($list, $divisions)
    {
        $accounts = (new ListService())->getAllList(
            null,
            null,
            [$list['line']],
            $divisions,
            $list['bricks'],
            $list['specialities'],
            $list['types']
        );
        return $accounts;
    }
    public function active($list, $divisions)
    {
        $accounts = (new ListService())->getActiveList(null, null, [$list['line']], $divisions, $list['bricks'], $list['specialities'], $list['types']);
        return $accounts;
    }
    public function inActive($list, $divisions)
    {
        $accounts = (new ListService())->getInactiveList(
            null,
            null,
            [$list['line']],
            $divisions,
            $list['bricks'],
            $list['specialities'],
            $list['types']
        );
        return $accounts;
    }

    public function save(Request $request)
    {
        $data = $request->chosenData;
        $accounts = $request->accounts;
        if ($data['action'] == null) {
            $this->updateAccountsInfo($accounts);
        } else {
            $isCopy = $data['action'] == 1;
            if ($isCopy) {
                $this->copyAccounts($accounts, $data);
            } else {
                $this->moveAccounts($accounts, $data);
            }
        }
        return $this->respondSuccess();
    }

    public function updateAccountsInfo($accounts)
    {
        // if (empty($accounts)) throw new Exception('PLease Check Accounts befor update any data');
        foreach ($accounts as $account) {
            Account::find($account['account_id'])->update([
                'mobile' => $account['acc_mobile'] ?? $account['doc_mobile'],
                'address' => $account['address'],
            ]);
            Doctor::find($account['doctor_id'])->update([
                'mobile' => $account['doc_mobile'] ?? $account['acc_mobile'],
            ]);
        }
        return $this->respondSuccess();
    }
    public function copyAccounts($accounts, $data)
    {
        $line = $data['line'];
        $division = $data['division'];
        $brick = $data['brick'];
        foreach ($accounts as $account) {
            $exists = AccountLines::select('line_id', 'line_division_id', 'brick_id', 'from_date', 'to_date')
                ->where('account_id', $account['account_id'])->where('line_id', $line);
            if (!isNullable($division)) {
                $exists = $exists->where('line_division_id', $division);
            }
            if (!isNullable($brick)) {
                $exists->where('brick_id', $brick);
            }
            $exists = $exists->first();
            if (!$exists && !isNullable($line) && $account['brick_id']) {
                $accountLine = Account::find($account['account_id'])->activeAccountLines()
                    ->where('account_lines.line_id', $account['line_id'])
                    ->where('account_lines.line_division_id', $account['div_id'])
                    ->where('account_lines.brick_id', $account['brick_id'])
                    ->first();
                if (isNullable($division)) {
                    $brickDivisions = LineBricks::select('line_id', 'brick_id', 'line_division_id', 'from_date', 'to_date')
                        ->where('line_id', $line)
                        ->where('brick_id', $accountLine['brick_id'])->where('from_date', '<=', now())
                        ->where(fn($q) => $q->where('to_date', '>', (string) Carbon::now())->orWhere('to_date', null))
                        ->get();
                    foreach ($brickDivisions as $brickDivision) {
                        $this->createCopyAccounts($accountLine, $line, $brickDivision->line_division_id, $brickDivision->brick_id, $data);
                    }
                }
                if (!isNullable($division)) {
                    if (isNullable($brick)) {
                        $brickId = LineBricks::select('line_id', 'brick_id', 'line_division_id', 'from_date', 'to_date')
                            ->where('line_id', $line)->where('line_division_id', $division)
                            ->where('brick_id', $account['brick_id'])->where('from_date', '<=', now())
                            ->where(fn($q) => $q->where('to_date', '>', (string) Carbon::now())->orWhere('to_date', null))
                            ->first()?->brick_id;
                        if ($brickId) {
                            $this->createCopyAccounts($accountLine, $line, $division, $brickId, $data);
                        }
                    } else {
                        $this->createCopyAccounts($accountLine, $line, $division, $brick, $data);
                    }
                }
            }
        }
        return $this->respondSuccess();
    }

    public function createCopyAccounts($accountLine, $line, $division, $brick, $data)
    {
        $newAccountLine = AccountLines::create([
            'account_id' => $accountLine->account_id,
            'line_id' => $line,
            'line_division_id' => $division,
            'brick_id' => $brick,
            'class_id' => $accountLine->class_id,
            'visit_id' => $accountLine->visit_id,
            'll' => $accountLine->ll,
            'lg' => $accountLine->lg,
            'from_date' => Carbon::parse($data['activeFrom'])->toDateString(),
            'to_date' => null,
        ]);
        $newAccountDoctors = NewAccountDoctor::where('account_id', $accountLine->account_id)
            ->where('line_id', $accountLine->line_id)
            ->where('new_account_doctors.from_date', '<=', (string)Carbon::now())
            ->where(fn($q) => $q->where('new_account_doctors.to_date', '=', null)
                ->orWhere('new_account_doctors.to_date', '>=', (string)Carbon::now()))
            ->get()->pluck('doctor_id');
        foreach ($newAccountDoctors as $newAccountDoctor) {
            NewAccountDoctor::create([
                'account_id' => $newAccountLine->account_id,
                'line_id' => $newAccountLine->line_id,
                'doctor_id' => $newAccountDoctor,
                'account_lines_id' => $newAccountLine->id,
                'from_date' => Carbon::parse($data['activeFrom'])->toDateString(),
                'to_date' => null,
            ]);
        }
    }
    public function moveAccounts($accounts, $data)
    {
        $accountIds = collect($accounts)->pluck('account_id')->unique()->values()->toArray();
        $accountDrIds = collect($accounts)->pluck('acc_dr_id')->unique()->values()->toArray();
        $doctorIds = collect($accounts)->pluck('doctor_id')->unique()->values()->toArray();
        if ($data['chosen_type_id'] == 1 && $data['type']) {
            Account::whereIntegerInRaw('id', $accountIds)->update(['type_id' => $data['type']]);
        }
        if ($data['chosen_type_id'] == 2 && $data['speciality']) {
            Doctor::whereIntegerInRaw('id', $doctorIds)->update(['speciality_id' => $data['speciality']]);
        }
        if ($data['chosen_type_id'] == 4 && $data['doc_class']) {
            Doctor::whereIntegerInRaw('id', $doctorIds)->update(['class_id' => $data['doc_class']]);
            NewAccountDoctor::whereIntegerInRaw('id', $accountDrIds)->update(['class_id' => $data['doc_class']]);
        }
        if ($data['chosen_type_id'] == 3 && $data['acc_class']) {
            $line = Line::find($accounts[0]['line_id']);
            AccountLines::where('line_id', $line->id)->whereIntegerInRaw('account_id', $accountIds)->update(['class_id' => $data['acc_class']]);
        }
        return $this->respondSuccess();
    }
}
