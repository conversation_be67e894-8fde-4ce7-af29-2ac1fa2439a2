<?php

namespace App\Http\Requests;

use App\ActualVisitSetting;
use App\Exceptions\CrmException;
use App\Helpers\CrmExcelDate;
use App\OwActualVisit;
use App\PlanSetting;
use App\Setting;
use App\UserActualStartDay;
use Attribute;
use Carbon\CarbonPeriod;
use Exception;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class OwActualVisitRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $acceptMoreThanOneOw = ActualVisitSetting::where('key', 'add_more_than_one_ow_at_same_day')->value('value') == 'Yes';
        if ($acceptMoreThanOneOw) return [];
        /**@var User $user */
        $user = Auth::user();
        /**@var User */
        $lineIds = $user->userLines()->pluck('id');

        $notesSetting = Setting::where('key', 'required_notes')->value('value');
        if ($user->hasRole('admin') || $user->hasRole('sub admin') || $user->hasRole('Gemstone Admin')) return [];
        $accept_officework_with_visits = ActualVisitSetting::where('key', 'accept_officework_with_visits')->value('value');
        $accept_visits_within_vacations = ActualVisitSetting::where('key', 'accept_visits_within_vacations')->value('value');
        $dates = collect([]);
        $period = CarbonPeriod::create($this->from_date, $this->to_date);
        foreach ($period as $date) {
            $dates->push($date->format('Y-m-d'));
        }
        foreach ($dates as $date) {
            $actualExtraTime = ActualVisitSetting::where('key', 'actual_extra_time')->value('value');
            $validDate = Carbon::parse($date)->startOfDay()->addHours($actualExtraTime)->toDateTimeString();
            if (
                Carbon::parse($date)->toDateTimeString() < Carbon::today()->toDateTimeString() &&
                Carbon::now()->toDateTimeString() > $validDate &&
                !UserActualStartDay::whereIn('line_id', $lineIds)
                    ->where(fn($q) => $q->where('user_id', 0)->orWhere('user_id', $user->id))
                    ->where('date', '<=', Carbon::parse($date)->toDateString())->exists() &&
                !ActualVisitSetting::where('key', 'specific_actual_start_day')->value('value')
            ) {
                throw new Exception('This date ' . Carbon::parse($date)->toDateString() . ' not valid because it exceeded ' . $validDate);
            }

            if ($accept_visits_within_vacations == 'No') {
                $vacationDates = $user->vacationDates();
                foreach ($vacationDates as $vacationDate) {
                    if ($vacationDate == Carbon::parse($date)->toDateString()) {
                        throw new Exception("There is vacation on " . Carbon::parse($date)->toDateString() . " , so you can't make any office work");
                    }
                }
            }
            if ($accept_officework_with_visits == 'No') {
                $visits = $user->actualVisits()->whereDate('visit_date', Carbon::parse($date)->toDateString());
                if ($visits->exists()) {
                    $visits->get()->each(function ($visit) use ($date) {
                        if ($visit->accountType->shift->id == $this->shift_id || isNullable($this->shift_id)) {
                            throw new Exception('The date ' . Carbon::parse($date)->toDateString() . ' not valid because user has visits at this date.');
                        }
                    });
                }
            }
            $offices = OwActualVisit::where('user_id', $user->id)->whereDate('date', Carbon::parse($date)->toDateString())->get();
            foreach ($offices as $office) {
                if (isNullable($office->shift_id) || $office->shift_id == $this->shift_id || isNullable($this->shift_id))
                    throw new Exception('The date ' . Carbon::parse($date)->toDateString() . ' not valid because user has office work at this date.');
            }
        }
        throw new CrmException([
            Carbon::parse($date)->toDateTimeString() < Carbon::today()->toDateTimeString(),
            Carbon::now()->toDateTimeString() > $validDate,
            !UserActualStartDay::whereIn('line_id', $lineIds)
                ->where(fn($q) => $q->where('user_id', 0)->orWhere('user_id', $user->id))
                ->where('date', '<=', Carbon::parse($date)->toDateString())->exists(),
            !ActualVisitSetting::where('key', 'specific_actual_start_day')->value('value')

        ]);
        return [
            // 'date' => [
            //     'required', 'string', 'max:191',
            // function ($attribute, $value, $fail) {
            //     $actualExtraTime = ActualVisitSetting::where('key', 'actual_extra_time')->value('value');
            //     $validDate = Carbon::today()->startOfDay()->addHours($actualExtraTime)->toDateTimeString();
            //     if (
            //         $value < Carbon::today()->toDateTimeString() &&
            //         Carbon::now()->toDateTimeString() > $validDate
            //     ) {
            //         $fail('This ' . $attribute . ' not valid because it exceeded ' . $validDate);
            //     }
            // }, 
            // function ($attribute, $value, $fail) use ($accept_visits_within_vacations, $user) {
            //     if ($accept_visits_within_vacations == 'No') {
            //         $dates = $user->vacationDates();
            //         foreach ($dates as $date) {
            //             if ($date == Carbon::parse($value)->toDateString()) {
            //                 $fail("There is vacation on this" . $attribute . " , so you can't make any office work");
            //             }
            //         }
            //     }
            // },
            // function ($attribute, $value, $fail) use ($accept_officework_with_visits, $user) {
            //     if ($accept_officework_with_visits == 'No') {
            //         $visits = $user->actualVisits()->whereDate('visit_date', Carbon::parse($value)->toDateString());
            //         if ($visits->exists()) {
            //             $visits->get()->each(function ($visit) use ($attribute, $value, $fail) {
            //                 if ($visit->accountType->shift->id == $this->shift_id || isNullable($this->shift_id)) {
            //                     $fail('The ' . $attribute . ' not valid because user has visits on ' . $value);
            //                 }
            //             });
            //         }
            //     }
            // },
            // function ($attribute, $value, $fail) use ($user) {
            //     $offices = $user->officeWork()->whereDate('date', Carbon::parse($value)->toDateString());
            //     if ($offices->exists()) {
            //         $offices->get()->each(function ($office) use ($attribute, $value, $fail) {
            //             if ($office->shift_id == $this->shift_id || isNullable($office->shift_id)) {
            //                 $fail('The ' . $attribute . ' not valid because user has office work at ' . $value);
            //             }
            //         });
            //     }
            // }
            // ],
            'ow_type_id' => ['required', 'integer', 'exists_not_soft_deleted:office_work_types,id'],
            'notes' => [Rule::requiredIf(fn() => $notesSetting == 'Yes')],
        ];
    }
}
