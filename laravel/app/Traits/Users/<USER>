<?php


namespace App\Traits\Users;

use App\ApprovablePlanable;
use App\DivisionType;
use App\Exceptions\CrmException;
use App\LineDivision;
use App\Models\CommercialRequest\CommercialRequest;
use App\Models\Expenses\Expense;
use App\User;
use App\Vacation;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

trait ApprovalFlowFilterData
{

    public function approvalWidget(
        $object,
        User $user,
        $model,
        $from = null,
        $to = null,
        ?Collection $lines = null,
        ?Collection $linesAapprovables = null,
        ?array $authLines = [],
        ?array $products = []
    ) {

        $linesPlanables = collect([]);
        Log::info('Approval : ');
        Log::info($object);
        Log::info($object->user);
        $objectUser = $object->user;
        if ($objectUser?->hasPosition()) {
            $linesPlanables->push($objectUser?->position()?->linePlanable()->first());
        } else {
            $lines->each(function ($line) use ($linesPlanables, $objectUser, $from, $to) {
                $linesPlanables = $linesPlanables->push($objectUser?->divisionType($line, $from, $to)?->linePlanable($line->id)->first());
            });
        }

        // $linesDataFlow = ApprovablePlanable::whereIn("approvable_id", $linesAapprovables->collapse()->toArray())
        //     ->whereIn("planable_id", $linesPlanables->pluck('id'))
        //     ->where('request_type', $model)
        //     ->orderBy('flow', 'DESC')
        //     ->first();
        // $allApprovables = collect([]);
        // Optimize fetching of `approvable_planable` in one query
        $linesPlanableIds = $linesPlanables->pluck('id')->toArray();
        $approvableIds = $linesAapprovables->collapse()->toArray();

        $linesDataFlow = Cache::remember("approvable_flow_{$model}_" . implode('_', $approvableIds) . '_' . implode('_', $linesPlanableIds), 24, function () use ($approvableIds, $linesPlanableIds, $model) {
            return ApprovablePlanable::whereIntegerInRaw("approvable_id", $approvableIds)
                ->whereIntegerInRaw("planable_id", $linesPlanableIds)
                ->where('request_type', $model)
                ->orderByDesc('flow')
                ->first();
        });

        // Optimized approvable fetching
        $allApprovables = Cache::remember("all_approvables_{$model}_" . implode('_', $linesPlanableIds), 24, function () use ($objectUser, $linesPlanableIds, $model, $from, $to) {
            if ($objectUser?->hasPosition()) {
                return ApprovablePlanable::select('positions.id', 'positions.name')
                    ->join('approvables', 'approvable_planable.approvable_id', '=', 'approvables.id')
                    ->join('positions', 'approvables.approvable_id', '=', 'positions.id')
                    ->where('approvable_planable.request_type', $model)
                    ->whereNull('approvables.line_id')
                    ->where('approvables.approvable_type', 'App\Position')
                    ->whereIn("approvable_planable.planable_id", $linesPlanableIds)
                    ->orderBy('approvable_planable.flow', 'ASC')
                    ->get();
            } else {
                return ApprovablePlanable::select('division_types.id', 'division_types.name', 'division_types.last_level')
                    ->join('approvables', 'approvable_planable.approvable_id', '=', 'approvables.id')
                    ->join('division_types', 'approvables.approvable_id', '=', 'division_types.id')
                    ->where('approvable_planable.request_type', $model)
                    ->where('approvables.line_id', $objectUser->lines($from, $to)?->first()?->id)
                    ->where('approvables.approvable_type', 'App\DivisionType')
                    ->whereIn("approvable_planable.planable_id", $linesPlanableIds)
                    ->orderBy('approvable_planable.flow', 'ASC')
                    ->get();
            }
        });

        // Fetch division types only once
        $division_types = Cache::remember(
            'level_division_types',
            24,
            fn() =>
            DivisionType::where('last_level', 0)->whereIn('id', $allApprovables->pluck('id'))->pluck('id')
        );

        $vacantCount = 0;

        if ($objectUser->hasPosition()) {
            $lines = $lines->pluck('id')->toArray();

            // // Convert $others to a set for fast lookups
            // $othersSet = array_flip($authLines);

            // $intersectedLines = [];
            // foreach ($lines as $line) {
            //     if (isset($othersSet[$line])) {
            //         $intersectedLines[] = $line;
            //     }
            // }
            for ($i = 0; $i < $linesDataFlow?->flow; $i++) {
                $userPosition = User::whereHas('allLines', function ($q) use (
                    $lines,
                    $to
                ) {
                    $q->whereIn('lines.id', $lines)
                        ->whereNull('line_users.deleted_at')
                        ->where(function ($q) use ($to) {
                            $q->where('line_users.to_date', '>=', Carbon::parse($to)->toDateString())
                                ->orWhereNull('line_users.to_date');
                        });
                })->whereHas('allPositions', function ($q) use ($to, $i, $allApprovables, $products, $model) {
                    $q->where('positions.id', $allApprovables[$i]['id'])
                        ->where(function ($q) use ($to) {
                            $q->whereNull('user_positions.to_date')
                                ->orWhere('user_positions.to_date', '>=', Carbon::parse($to)->toDateString());
                        });
                    if ($model == CommercialRequest::class) {
                        $q->where(function ($query) use ($products) {
                            $query->whereExists(function ($q) use ($products) {
                                $q->select(DB::raw(1))
                                    ->from('line_product_positions')
                                    ->whereColumn('line_product_positions.user_position_id', 'user_positions.id')
                                    ->whereNull('line_product_positions.deleted_at')
                                    ->whereIn('line_product_positions.product_id', $products);
                            })
                                ->orWhereNotExists(function ($q) {
                                    $q->select(DB::raw(1))
                                        ->from('line_product_positions')
                                        ->whereColumn('line_product_positions.user_position_id', 'user_positions.id')
                                        ->whereNull('line_product_positions.deleted_at');
                                });
                        });
                    }
                })
                    ->first();
                echo $userPosition . "\n" . "\n";
                if (!$userPosition) $vacantCount++;
            }
        } else {
            // throw new CrmException($division_types);
            $lines->each(function ($line) use ($objectUser, $user, $allApprovables, &$vacantCount, $from, $to, $division_types) {
                $userHasLineDivision = Cache::remember('user => ' . $user->id . ' _has_line => ' . $line->id . ' _division', 24, fn() => $user->hasDivision($line));
                if ($userHasLineDivision) {
                    $directBelow = $user->divisionType($line, $from, $to)?->descendants()->whereIn('id', $division_types)->where('last_level', 0)->first();

                    $directDivisions = $user->allBelowDivisions($line, $from, $to)
                        ->whereIn('division_type_id', $division_types)->unique('id')->values();
                    $objectUserDivisions = $objectUser->divisions($from, $to)->where('line_divisions.line_id', $line->id)->get()->pluck('id')->toArray();
                    $allApprovables->each(function ($approvable) use ($objectUserDivisions, $directBelow, $directDivisions, &$vacantCount, $from, $to) {
                        if ($directBelow?->id == $approvable?->id) {
                            $directDivisions->each(function ($division) use ($objectUserDivisions, &$vacantCount, $from, $to) {
                                $belowDivisions = $division->getBelowDivisions($from, $to)->pluck('id')->unique()->toArray();
                                $intersect = array_intersect($belowDivisions, $objectUserDivisions);
                                $user = $division->user($from, $to);
                                $vacant = null;
                                if ($user) {
                                    $vacant = stristr($user->fullname, 'vacant');
                                }
                                if ((!$user || $vacant) && !empty($intersect)) {
                                    $vacantCount += 1;
                                }
                            });
                        }
                    });
                }
                $userHasPosition = Cache::remember('user => ' . $user->id . ' _has_position', 24, fn() => $user->hasPosition());
                if ($userHasPosition && !$userHasLineDivision && $objectUser->hasDivision($line)) {
                    $userPosition = $this->userPosition->first();
                    $division = Cache::remember('user_position => ' . $userPosition->id . ' _division_of_line => ' . $line->id, 24, fn() => $userPosition->divisions()->where('line_id', $line->id)->orderBy('division_type_id', 'Asc')->first());

                    if (!$division) {
                        $division = Cache::remember('country_of_line => ' . $line?->id, 24, fn() => LineDivision::where('line_id', $line?->id)->where('division_type_id', 1)->first());
                    }
                    $directBelow = Cache::remember('last_level_under_country_of_division => ' . $division?->id . 'of_line => ' . $line?->id, 24, fn() => $division?->DivisionType()->where('last_level', 0)->first());
                    $directDivisions = LineDivision::where('line_id', $line?->id)
                        ->whereIn('division_type_id', $allApprovables->pluck('id')->toArray())->get()->unique('id')->values();
                    $objectUserDivisions = $objectUser->divisions($from, $to)->where('line_divisions.line_id', $line?->id)->get()->pluck('id')->toArray();
                    $allApprovables->each(function ($approvable) use ($objectUserDivisions, $directBelow, $directDivisions, &$vacantCount, $from, $to) {
                        if ($directBelow?->id == $approvable?->id) {
                            $directDivisions->each(function ($division) use ($objectUserDivisions, &$vacantCount, $from, $to) {
                                $belowDivisions = $division->getBelowDivisions($from, $to)->pluck('id')->unique()->values()->toArray();
                                $intersect = array_intersect($belowDivisions, $objectUserDivisions);
                                $user = $division->user($from, $to);
                                $vacant = null;
                                if ($user) {
                                    $vacant = stristr($user->fullname, 'vacant');
                                }
                                if ((!$user || $vacant) && !empty($intersect)) {
                                    $vacantCount += 1;
                                }
                            });
                        }
                    });
                }
            });
        }
        return array(
            'linesDataFlow' => $linesDataFlow,
            'vacantCount' => $vacantCount,
        );
    }
}
